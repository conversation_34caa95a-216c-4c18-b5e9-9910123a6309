# ZhipuAI Embedding MLflow Error Fix

## Problem Description

When running MLflow evaluation with ZhipuAIEmbedding, you may encounter these errors:

### Error 1: Missing API Key
```
TypeError: ZhipuAIEmbedding.__init__() missing 1 required positional argument: 'api_key'
```

### Error 2: Unknown Model URI
```
MlflowException: Unknown model uri prefix 'deepseek'
```

## Root Cause

These errors occur because:

### Error 1: Missing API Key
1. **MLflow Security Feature**: MLflow automatically removes API keys from serialized models for security reasons
2. **Deserialization Issue**: When MLflow loads the model for evaluation, it tries to recreate the ZhipuAIEmbedding instance
3. **Missing API Key**: The ZhipuAIEmbedding constructor requires an `api_key` parameter, but it's not available during deserialization

### Error 2: Unknown Model URI
1. **Unsupported URI Format**: MLflow's `answer_correctness` metric doesn't recognize the `deepseek:/` URI prefix
2. **Limited Model Support**: MLflow only supports specific model URI formats like `openai:/`, `anthropic:/`, etc.

## Solution

The fix involves two steps:
1. Setting API keys as environment variables (for the ZhipuAI error)
2. Using a supported model URI for answer_correctness (for the DeepSeek error)

### Step 1: Set Environment Variables

Add this code before running MLflow evaluation:

```python
import os

# Set API keys as environment variables
os.environ["DEEPSEEK_API_KEY"] = "your-deepseek-api-key"
os.environ["ZHIPUAI_API_KEY"] = "your-zhipuai-api-key"

# Configure MLflow to use DeepSeek endpoint for OpenAI-compatible evaluation
os.environ["OPENAI_API_KEY"] = "your-deepseek-api-key"
os.environ["OPENAI_API_BASE"] = "https://api.deepseek.com"
```

### Step 2: Update Model Configuration

Ensure your models are configured properly:

```python
from llama_index.llms.deepseek import DeepSeek
from llama_index.embeddings.zhipuai import ZhipuAIEmbedding
from llama_index.core import Settings

# Configure models (API keys will be read from environment)
Settings.llm = DeepSeek("deepseek-chat", api_key="your-deepseek-api-key")
Settings.embed_model = ZhipuAIEmbedding(
    "embedding-3", 
    dimensions=2048, 
    api_key="your-zhipuai-api-key"
)
```

### Step 3: Fix Model URI for Answer Correctness

Change the answer_correctness metric to use the OpenAI-compatible URI:

```python
# Change from:
answer_correctness("deepseek:/deepseek-chat")

# To (now using DeepSeek through OpenAI-compatible API):
answer_correctness("openai:/deepseek-chat")
```

### Step 4: Run Evaluation

Now you can run the MLflow evaluation without errors:

```python
from mlflow.metrics import latency
from mlflow.metrics.genai import answer_correctness

for model_info in models:
    with mlflow.start_run(run_id=model_info.run_id):
        result = mlflow.evaluate(
            model=model_info.model_uri,
            data=eval_df,
            targets="ground_truth",
            extra_metrics=[
                latency(),
                answer_correctness("openai:/gpt-4o-mini"),  # Fixed URI
            ],
            evaluator_config={"col_mapping": {"inputs": "query"}},
        )
```

## Files Modified

1. **Tutorial.ipynb**: Added environment variable setup before evaluation
2. **fix_zhipuai_error.py**: Standalone script demonstrating the fix
3. **ZHIPUAI_FIX.md**: This documentation file

## Testing the Fix

Run the fix script to verify everything works:

```bash
python fix_zhipuai_error.py
```

## Alternative Solutions

If the environment variable approach doesn't work, consider:

1. **Switch to OpenAI Embeddings**: Better MLflow integration
2. **Use a different embedding provider**: Such as Hugging Face embeddings
3. **Custom wrapper**: Create a wrapper class that handles API keys more gracefully

## Security Note

Remember to:
- Never commit API keys to version control
- Use environment variables or secure key management systems
- Rotate API keys regularly
