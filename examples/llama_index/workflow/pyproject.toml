[tool.poetry]
package-mode = false

[tool.poetry.dependencies]
python = ">=3.9,<3.13"
mlflow = ">=2.17.0"
llama-index = ">=0.11.0"
llama-index-postprocessor-rankgpt-rerank = "*"
llama-index-readers-web = "*"
llama-index-retrievers-bm25 = "*"
llama-index-tools-tavily-research = "*"
llama-index-utils-workflow = "*"
llama-index-vector-stores-qdrant = "*"

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"
