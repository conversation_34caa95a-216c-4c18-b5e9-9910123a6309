# Quick Fix for MLflow Evaluation Errors

## TL;DR - Using DeepSeek with ML<PERSON>

If you're getting errors when running MLflow evaluation with DeepSeek, here's the complete fix:

### 1. Add this cell before your evaluation code:

```python
import os

# Set API keys as environment variables
os.environ["DEEPSEEK_API_KEY"] = "***********************************"
os.environ["ZHIPUAI_API_KEY"] = "b416ef77699448858ba295b103ded93c.MHsFdHEvoG2MFKjT"

# Configure MLflow to use DeepSeek endpoint for OpenAI-compatible evaluation
# This is the correct way according to MLflow documentation
os.environ["OPENAI_API_KEY"] = "***********************************"
os.environ["OPENAI_API_BASE"] = "https://api.deepseek.com"

print("✅ DeepSeek configured for MLflow evaluation")
```

### 2. Change your evaluation code:

```python
# Change this line:
answer_correctness("deepseek:/deepseek-chat")

# To this (now using DeepSeek through OpenAI-compatible API):
answer_correctness("openai:/deepseek-chat")
```

### 3. Run the evaluation again

That's it! Your evaluation should now work with DeepSeek without errors.

## What this fixes:

- ✅ `TypeError: ZhipuAIEmbedding.__init__() missing 1 required positional argument: 'api_key'`
- ✅ `MlflowException: Unknown model uri prefix 'deepseek'`

## Why this works:

1. **Environment variables**: MLflow expects API keys to be in environment variables for security
2. **Supported model URI**: MLflow only supports certain model URI formats for evaluation metrics

For detailed explanation, see `ZHIPUAI_FIX.md`.
