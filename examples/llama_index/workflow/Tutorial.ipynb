import mlflow

mlflow.set_experiment("LlamaIndex Workflow RAG")

import os
os.environ["DEEPSEEK_API_KEY"] = "***********************************"
os.environ["ZHIPUAI_API_KEY"] = "b416ef77699448858ba295b103ded93c.MHsFdHEvoG2MFKjT"

from llama_index.core import Settings
from llama_index.embeddings.openai import OpenAIEmbedding
from llama_index.llms.openai import OpenAI

# LlamaIndex by default uses OpenAI APIs for LLMs and embeddings models. You can use the default
# model (`gpt-3.5-turbo` and `text-embeddings-ada-002` as of Oct 2024), but we recommend using the
# latest efficient models instead for getting better results with lower cost.
Settings.embed_model = OpenAIEmbedding(model="text-embedding-3-small")
Settings.llm = OpenAI(model="gpt-4o-mini")

from llama_index.llms.deepseek import DeepSeek
from llama_index.embeddings.zhipuai import ZhipuAIEmbedding
from llama_index.core import Settings
from llama_index.embeddings.ollama import OllamaEmbedding

Settings.llm=DeepSeek("deepseek-chat","***********************************")
# Settings.embed_model=ZhipuAIEmbedding("embedding-3",dimensions=2048, api_key="b416ef77699448858ba295b103ded93c.MHsFdHEvoG2MFKjT")
Settings.embed_model = OllamaEmbedding(
    model_name="nomic-embed-text",
    base_url="http://localhost:11434"
)

import getpass
import os

os.environ["TAVILY_AI_API_KEY"] = "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"

from llama_index.readers.web import SimpleWebPageReader

with open("data/urls.txt") as file:
    urls = [line.strip() for line in file if line.strip()]

documents = SimpleWebPageReader(html_to_text=True).load_data(urls)

import qdrant_client
from llama_index.vector_stores.qdrant import QdrantVectorStore

client = qdrant_client.QdrantClient(host="localhost", port=6333)
vector_store = QdrantVectorStore(client=client, collection_name="mlflow_doc")

from llama_index.core import StorageContext, VectorStoreIndex

storage_context = StorageContext.from_defaults(vector_store=vector_store)
index = VectorStoreIndex.from_documents(documents=documents, storage_context=storage_context)

from llama_index.core.node_parser import SentenceSplitter
from llama_index.retrievers.bm25 import BM25Retriever

splitter = SentenceSplitter(chunk_size=512)
nodes = splitter.get_nodes_from_documents(documents)
bm25_retriever = BM25Retriever.from_defaults(nodes=nodes)
bm25_retriever.persist(".bm25_retriever")

# Workflow with VS + BM25 retrieval
from workflow.workflow import HybridRAGWorkflow

workflow = HybridRAGWorkflow(retrievers=["vector_search", "bm25"], timeout=60)
response = await workflow.run(query="Why use MLflow with LlamaIndex?")
print(response)

# Different configurations we will evaluate. We don't run evaluation for all permutation
# for demonstration purpose, but you can add as many patterns as you want.
run_name_to_retrievers = {
    # 1. No retrievers (prior knowledge in LLM).
    "none": [],
    # 2. Vector search retrieval only.
    "vs": ["vector_search"],
    # 3. Vector search and keyword search (BM25)
    "vs + bm25": ["vector_search", "bm25"],
    # 4. All retrieval methods including web search.
    "vs + bm25 + web": ["vector_search", "bm25", "web_search"],
}

# Create an MLflow Run and log model with each configuration.
models = []
for run_name, retrievers in run_name_to_retrievers.items():
    with mlflow.start_run(run_name=run_name):
        model_info = mlflow.llama_index.log_model(
            # Specify the model Python script.
            llama_index_model="workflow/model.py",
            # Specify retrievers to use.
            model_config={"retrievers": retrievers},
            # Define dependency files to save along with the model
            code_paths=["workflow"],
            # Subdirectory to save artifacts (not important)
            name="model",
        )
        models.append(model_info)

mlflow.llama_index.autolog()

import pandas as pd

eval_df = pd.read_csv("data/mlflow_qa_dataset.csv")
display(eval_df.head(3))

import os

# Set API keys as environment variables for MLflow model loading
os.environ["DEEPSEEK_API_KEY"] = "***********************************"
os.environ["ZHIPUAI_API_KEY"] = "b416ef77699448858ba295b103ded93c.MHsFdHEvoG2MFKjT"

# Configure MLflow to use DeepSeek endpoint for OpenAI-compatible evaluation
# This is the correct way to use custom OpenAI-compatible endpoints with MLflow
os.environ["OPENAI_API_KEY"] = "***********************************"
os.environ["OPENAI_API_BASE"] = "https://api.deepseek.com"

print("✅ DeepSeek configured for MLflow evaluation")
print("✅ API keys set as environment variables")
print("✅ OPENAI_API_BASE set to DeepSeek endpoint")

from mlflow.metrics import latency
from mlflow.metrics.genai import answer_correctness

for model_info in models:
    with mlflow.start_run(run_id=model_info.run_id):
        result = mlflow.evaluate(
            # Pass the URI of the logged model above
            model=model_info.model_uri,
            data=eval_df[:1],
            # Specify the column for ground truth answers.
            targets="ground_truth",
            # Define the metrics to compute.
            extra_metrics=[
                latency(),
                answer_correctness("openai:/deepseek-chat"),
            ],
            # The answer_correctness metric requires "inputs" column to be
            # present in the dataset. We have "query" instead so need to
            # specify the mapping in `evaluator_config` parameter.
            evaluator_config={"col_mapping": {"inputs": "query"}},
        )