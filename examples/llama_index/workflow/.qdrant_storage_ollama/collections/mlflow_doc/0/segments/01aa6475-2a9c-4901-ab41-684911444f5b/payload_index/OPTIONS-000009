# This is a RocksDB option file.
#
# For detailed file format spec, please refer to the example file
# in examples/rocksdb_option_file_example.ini
#

[Version]
  rocksdb_version=9.9.3
  options_file_version=1.1

[DBOptions]
  compaction_readahead_size=2097152
  strict_bytes_per_sync=false
  bytes_per_sync=0
  max_background_jobs=2
  avoid_flush_during_shutdown=false
  max_background_flushes=-1
  delayed_write_rate=16777216
  max_open_files=256
  max_subcompactions=1
  writable_file_max_buffer_size=1048576
  wal_bytes_per_sync=0
  max_background_compactions=-1
  max_total_wal_size=0
  delete_obsolete_files_period_micros=180000000
  stats_dump_period_sec=600
  stats_history_buffer_size=1048576
  stats_persist_period_sec=600
  follower_refresh_catchup_period_ms=10000
  enforce_single_del_contracts=true
  lowest_used_cache_tier=kNonVolatileBlockTier
  bgerror_resume_retry_interval=1000000
  metadata_write_temperature=kUnknown
  best_efforts_recovery=false
  log_readahead_size=0
  write_identity_file=true
  write_dbid_to_manifest=true
  prefix_seek_opt_in_only=false
  wal_compression=kNoCompression
  manual_wal_flush=false
  db_host_id=__hostname__
  two_write_queues=false
  random_access_max_buffer_size=1048576
  avoid_unnecessary_blocking_io=false
  skip_checking_sst_file_sizes_on_db_open=false
  flush_verify_memtable_count=true
  fail_if_options_file_error=true
  atomic_flush=false
  verify_sst_unique_id_in_manifest=true
  skip_stats_update_on_db_open=false
  track_and_verify_wals_in_manifest=false
  compaction_verify_record_count=true
  paranoid_checks=true
  create_if_missing=true
  max_write_batch_group_size_bytes=1048576
  follower_catchup_retry_count=10
  avoid_flush_during_recovery=false
  file_checksum_gen_factory=nullptr
  enable_thread_tracking=false
  allow_fallocate=true
  allow_data_in_errors=false
  error_if_exists=false
  use_direct_io_for_flush_and_compaction=false
  background_close_inactive_wals=false
  create_missing_column_families=true
  WAL_size_limit_MB=0
  use_direct_reads=false
  persist_stats_to_disk=false
  allow_2pc=false
  is_fd_close_on_exec=true
  max_log_file_size=1048576
  max_file_opening_threads=16
  wal_filter=nullptr
  wal_write_temperature=kUnknown
  follower_catchup_retry_wait_ms=100
  allow_mmap_reads=false
  allow_mmap_writes=false
  use_adaptive_mutex=false
  use_fsync=false
  table_cache_numshardbits=6
  dump_malloc_stats=false
  db_write_buffer_size=0
  allow_ingest_behind=false
  keep_log_file_num=1
  max_bgerror_resume_count=2147483647
  allow_concurrent_memtable_write=true
  recycle_log_file_num=0
  log_file_time_to_roll=0
  manifest_preallocation_size=4194304
  enable_write_thread_adaptive_yield=true
  WAL_ttl_seconds=0
  max_manifest_file_size=1073741824
  wal_recovery_mode=kTolerateCorruptedTailRecords
  enable_pipelined_write=false
  write_thread_slow_yield_usec=3
  unordered_write=false
  write_thread_max_yield_usec=100
  advise_random_on_open=true
  info_log_level=ERROR_LEVEL
  

[CFOptions "default"]
  memtable_max_range_deletions=0
  compression_opts={checksum=false;max_dict_buffer_bytes=0;enabled=false;max_dict_bytes=0;max_compressed_bytes_per_kb=896;parallel_threads=1;zstd_max_train_bytes=0;level=32767;use_zstd_dict_trainer=true;strategy=0;window_bits=-14;}
  paranoid_memory_checks=false
  block_protection_bytes_per_key=0
  uncache_aggressiveness=0
  bottommost_file_compaction_delay=0
  memtable_protection_bytes_per_key=0
  experimental_mempurge_threshold=0.000000
  bottommost_compression=kDisableCompressionOption
  sample_for_compression=0
  prepopulate_blob_cache=kDisable
  blob_file_starting_level=0
  blob_compaction_readahead_size=0
  table_factory=BlockBasedTable
  max_successive_merges=0
  max_write_buffer_number=2
  prefix_extractor=nullptr
  memtable_huge_page_size=0
  write_buffer_size=10485760
  strict_max_successive_merges=false
  arena_block_size=1048576
  level0_file_num_compaction_trigger=4
  report_bg_io_stats=false
  inplace_update_num_locks=10000
  memtable_prefix_bloom_size_ratio=0.000000
  level0_stop_writes_trigger=36
  blob_compression_type=kNoCompression
  level0_slowdown_writes_trigger=20
  hard_pending_compaction_bytes_limit=274877906944
  target_file_size_multiplier=1
  bottommost_compression_opts={checksum=false;max_dict_buffer_bytes=0;enabled=false;max_dict_bytes=0;max_compressed_bytes_per_kb=896;parallel_threads=1;zstd_max_train_bytes=0;level=32767;use_zstd_dict_trainer=true;strategy=0;window_bits=-14;}
  paranoid_file_checks=false
  blob_garbage_collection_force_threshold=1.000000
  enable_blob_files=false
  soft_pending_compaction_bytes_limit=68719476736
  target_file_size_base=67108864
  max_compaction_bytes=1677721600
  disable_auto_compactions=false
  min_blob_size=0
  memtable_whole_key_filtering=false
  max_bytes_for_level_base=268435456
  last_level_temperature=kUnknown
  preserve_internal_time_seconds=0
  compaction_options_fifo={file_temperature_age_thresholds=;allow_compaction=false;age_for_warm=0;max_table_files_size=1073741824;}
  max_bytes_for_level_multiplier=10.000000
  max_bytes_for_level_multiplier_additional=1:1:1:1:1:1:1
  max_sequential_skip_in_iterations=8
  compression=kLZ4Compression
  default_write_temperature=kUnknown
  compaction_options_universal={incremental=false;compression_size_percent=-1;allow_trivial_move=false;max_size_amplification_percent=200;max_merge_width=4294967295;stop_style=kCompactionStopStyleTotalSize;min_merge_width=2;max_read_amp=-1;size_ratio=1;}
  blob_garbage_collection_age_cutoff=0.250000
  ttl=2592000
  periodic_compaction_seconds=0
  preclude_last_level_data_seconds=0
  blob_file_size=268435456
  enable_blob_garbage_collection=false
  persist_user_defined_timestamps=true
  compaction_pri=kMinOverlappingRatio
  compaction_filter_factory=nullptr
  comparator=leveldb.BytewiseComparator
  bloom_locality=0
  merge_operator=nullptr
  compaction_filter=nullptr
  level_compaction_dynamic_level_bytes=true
  optimize_filters_for_hits=false
  inplace_update_support=false
  max_write_buffer_number_to_maintain=0
  max_write_buffer_size_to_maintain=0
  sst_partitioner_factory=nullptr
  default_temperature=kUnknown
  compaction_style=kCompactionStyleLevel
  min_write_buffer_number_to_merge=1
  memtable_factory=SkipListFactory
  memtable_insert_with_hint_prefix_extractor=nullptr
  force_consistency_checks=true
  num_levels=7
  
[TableOptions/BlockBasedTable "default"]
  num_file_reads_for_auto_readahead=2
  initial_auto_readahead_size=8192
  metadata_cache_options={unpartitioned_pinning=kFallback;partition_pinning=kFallback;top_level_index_pinning=kFallback;}
  enable_index_compression=true
  verify_compression=false
  prepopulate_block_cache=kDisable
  format_version=6
  use_delta_encoding=true
  pin_top_level_index_and_filter=true
  read_amp_bytes_per_bit=0
  decouple_partitioned_filters=false
  partition_filters=false
  metadata_block_size=4096
  max_auto_readahead_size=262144
  index_block_restart_interval=1
  block_size_deviation=10
  block_size=4096
  detect_filter_construct_corruption=false
  no_block_cache=false
  checksum=kXXH3
  filter_policy=nullptr
  data_block_hash_table_util_ratio=0.750000
  block_restart_interval=16
  index_type=kBinarySearch
  pin_l0_filter_and_index_blocks_in_cache=false
  data_block_index_type=kDataBlockBinarySearch
  cache_index_and_filter_blocks_with_high_priority=true
  whole_key_filtering=true
  index_shortening=kShortenSeparators
  cache_index_and_filter_blocks=false
  block_align=false
  optimize_filters_for_memory=true
  flush_block_policy_factory=FlushBlockBySizePolicyFactory
  

[CFOptions "doc_id_map"]
  memtable_max_range_deletions=0
  compression_opts={checksum=false;max_dict_buffer_bytes=0;enabled=false;max_dict_bytes=0;max_compressed_bytes_per_kb=896;parallel_threads=1;zstd_max_train_bytes=0;level=32767;use_zstd_dict_trainer=true;strategy=0;window_bits=-14;}
  paranoid_memory_checks=false
  block_protection_bytes_per_key=0
  uncache_aggressiveness=0
  bottommost_file_compaction_delay=0
  memtable_protection_bytes_per_key=0
  experimental_mempurge_threshold=0.000000
  bottommost_compression=kDisableCompressionOption
  sample_for_compression=0
  prepopulate_blob_cache=kDisable
  blob_file_starting_level=0
  blob_compaction_readahead_size=0
  table_factory=BlockBasedTable
  max_successive_merges=0
  max_write_buffer_number=2
  prefix_extractor=nullptr
  memtable_huge_page_size=0
  write_buffer_size=10485760
  strict_max_successive_merges=false
  arena_block_size=1048576
  level0_file_num_compaction_trigger=4
  report_bg_io_stats=false
  inplace_update_num_locks=10000
  memtable_prefix_bloom_size_ratio=0.000000
  level0_stop_writes_trigger=36
  blob_compression_type=kNoCompression
  level0_slowdown_writes_trigger=20
  hard_pending_compaction_bytes_limit=274877906944
  target_file_size_multiplier=1
  bottommost_compression_opts={checksum=false;max_dict_buffer_bytes=0;enabled=false;max_dict_bytes=0;max_compressed_bytes_per_kb=896;parallel_threads=1;zstd_max_train_bytes=0;level=32767;use_zstd_dict_trainer=true;strategy=0;window_bits=-14;}
  paranoid_file_checks=false
  blob_garbage_collection_force_threshold=1.000000
  enable_blob_files=false
  soft_pending_compaction_bytes_limit=68719476736
  target_file_size_base=67108864
  max_compaction_bytes=1677721600
  disable_auto_compactions=false
  min_blob_size=0
  memtable_whole_key_filtering=false
  max_bytes_for_level_base=268435456
  last_level_temperature=kUnknown
  preserve_internal_time_seconds=0
  compaction_options_fifo={file_temperature_age_thresholds=;allow_compaction=false;age_for_warm=0;max_table_files_size=1073741824;}
  max_bytes_for_level_multiplier=10.000000
  max_bytes_for_level_multiplier_additional=1:1:1:1:1:1:1
  max_sequential_skip_in_iterations=8
  compression=kLZ4Compression
  default_write_temperature=kUnknown
  compaction_options_universal={incremental=false;compression_size_percent=-1;allow_trivial_move=false;max_size_amplification_percent=200;max_merge_width=4294967295;stop_style=kCompactionStopStyleTotalSize;min_merge_width=2;max_read_amp=-1;size_ratio=1;}
  blob_garbage_collection_age_cutoff=0.250000
  ttl=2592000
  periodic_compaction_seconds=0
  preclude_last_level_data_seconds=0
  blob_file_size=268435456
  enable_blob_garbage_collection=false
  persist_user_defined_timestamps=true
  compaction_pri=kMinOverlappingRatio
  compaction_filter_factory=nullptr
  comparator=leveldb.BytewiseComparator
  bloom_locality=0
  merge_operator=nullptr
  compaction_filter=nullptr
  level_compaction_dynamic_level_bytes=true
  optimize_filters_for_hits=false
  inplace_update_support=false
  max_write_buffer_number_to_maintain=0
  max_write_buffer_size_to_maintain=0
  sst_partitioner_factory=nullptr
  default_temperature=kUnknown
  compaction_style=kCompactionStyleLevel
  min_write_buffer_number_to_merge=1
  memtable_factory=SkipListFactory
  memtable_insert_with_hint_prefix_extractor=nullptr
  force_consistency_checks=true
  num_levels=7
  
[TableOptions/BlockBasedTable "doc_id_map"]
  num_file_reads_for_auto_readahead=2
  initial_auto_readahead_size=8192
  metadata_cache_options={unpartitioned_pinning=kFallback;partition_pinning=kFallback;top_level_index_pinning=kFallback;}
  enable_index_compression=true
  verify_compression=false
  prepopulate_block_cache=kDisable
  format_version=6
  use_delta_encoding=true
  pin_top_level_index_and_filter=true
  read_amp_bytes_per_bit=0
  decouple_partitioned_filters=false
  partition_filters=false
  metadata_block_size=4096
  max_auto_readahead_size=262144
  index_block_restart_interval=1
  block_size_deviation=10
  block_size=4096
  detect_filter_construct_corruption=false
  no_block_cache=false
  checksum=kXXH3
  filter_policy=nullptr
  data_block_hash_table_util_ratio=0.750000
  block_restart_interval=16
  index_type=kBinarySearch
  pin_l0_filter_and_index_blocks_in_cache=false
  data_block_index_type=kDataBlockBinarySearch
  cache_index_and_filter_blocks_with_high_priority=true
  whole_key_filtering=true
  index_shortening=kShortenSeparators
  cache_index_and_filter_blocks=false
  block_align=false
  optimize_filters_for_memory=true
  flush_block_policy_factory=FlushBlockBySizePolicyFactory
  
