{"_node_content":"{\"id_\": \"bc3cea5b-cdb7-419a-b87a-62fec6cb376d\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/auth/index.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"66af96b8a5a5e2a9450a6953a325de5aa0857ddbc581614eeef933515244f1f2\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"\", \"mimetype\": \"text/plain\", \"start_char_idx\": 0, \"end_char_idx\": 0, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/auth/index.html","doc_id":"https://mlflow.org/docs/latest/auth/index.html","ref_doc_id":"https://mlflow.org/docs/latest/auth/index.html"}
{"_node_content":"{\"id_\": \"8be72734-15ae-4778-be3b-55472373cdd6\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/auth/python-api.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"f294cc47573dd5d5fd1fce8fda3f62595b00d1a125103aeee2bc61ffdc3b45d4\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"d0704d9b-cb01-44e0-a98d-e195562d2e60\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"e5b226906777e0b4f8b1d0fc50d02f707ba492af6ef7f9cbb5812a769d078138\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"__[ ![MLflow](../_static/MLflow-logo-final-black.png) ](../index.html) [Main\\nDocs](/docs/latest) API Documentation\\n\\n3.1.0\\n\\n[Home](../index.html)\\n\\n  * [Python API](../python_api/index.html)\\n  * [Command-Line Interface](../cli.html)\\n  * MLflow Authentication Python API\\n    * mlflow.server.auth.client\\n    * mlflow.server.auth.entities\\n  * [MLflow Authentication REST API](rest-api.html)\\n  * [R API](../R-api.html)\\n  * [Java API](../java_api/index.html)\\n  * [REST API](../rest-api.html)\\n\\n[Contribute](https://github.com/mlflow/mlflow/blob/master/CONTRIBUTING.md)\\n\\n  * [Documentation](../index.html)\\n  * MLflow Authentication Python API\\n\\n# MLflow Authentication Python API\\n\\n## mlflow.server.auth.client\\n\\n_class\\n_mlflow.server.auth.client.AuthServiceClient[[source]](../_modules/mlflow/server/auth/client.html#AuthServiceClient)\\n\\n    \\n\\nBases: `object`\\n\\nClient of an MLflow Tracking Server that enabled the default basic\\nauthentication plugin. It is recommended to use\\n[`mlflow.server.get_app_client()`](../python_api/mlflow.server.html#mlflow.server.get_app_client\\n\\\"mlflow.server.get_app_client\\\") to instantiate this class. See\\n<https://mlflow.org/docs/latest/auth.html> for more information.\\n\\ncreate_experiment_permission(_experiment_id : str_, _username : str_,\\n_permission :\\nstr_)[[source]](../_modules/mlflow/server/auth/client.html#AuthServiceClient.create_experiment_permission)\\n\\n    \\n\\nCreate a permission on an experiment for a user.\\n\\nParameters\\n\\n    \\n\\n  * **experiment_id** \\u00e2\\u0080\\u0093 The id of the experiment.\\n\\n  * **username** \\u00e2\\u0080\\u0093 The username.\\n\\n  * **permission** \\u00e2\\u0080\\u0093 Permission to grant. Must be one of \\u00e2\\u0080\\u009cREAD\\u00e2\\u0080\\u009d, \\u00e2\\u0080\\u009cEDIT\\u00e2\\u0080\\u009d, \\u00e2\\u0080\\u009cMANAGE\\u00e2\\u0080\\u009d and \\u00e2\\u0080\\u009cNO_PERMISSIONS\\u00e2\\u0080\\u009d.\\n\\nRaises\\n\\n    \\n\\n[**mlflow.exceptions.RestException**](../python_api/exceptions/mlflow.exceptions.html#mlflow.exceptions.RestException\\n\\\"mlflow.exceptions.RestException\\\") \\u00e2\\u0080\\u0093 if the user does not exist, or a\\npermission already exists for this experiment user pair, or if the permission\\nis invalid.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 0, \"end_char_idx\": 1961, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/auth/python-api.html","doc_id":"https://mlflow.org/docs/latest/auth/python-api.html","ref_doc_id":"https://mlflow.org/docs/latest/auth/python-api.html"}
{"_node_content":"{\"id_\": \"d0704d9b-cb01-44e0-a98d-e195562d2e60\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/auth/python-api.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"f294cc47573dd5d5fd1fce8fda3f62595b00d1a125103aeee2bc61ffdc3b45d4\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"8be72734-15ae-4778-be3b-55472373cdd6\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"03c3278f90562702d847f6b24817790d22f46f0985b06d3bd66b2fbf26aef098\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"645ee7b7-e416-4938-9131-575e54a8549f\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"4e4a50c1b79a2bc09f62f28cc5083b5e3f76a7e735cce467205eb414d6f51b48\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"create_experiment_permission(_experiment_id : str_, _username : str_,\\n_permission :\\nstr_)[[source]](../_modules/mlflow/server/auth/client.html#AuthServiceClient.create_experiment_permission)\\n\\n    \\n\\nCreate a permission on an experiment for a user.\\n\\nParameters\\n\\n    \\n\\n  * **experiment_id** \\u00e2\\u0080\\u0093 The id of the experiment.\\n\\n  * **username** \\u00e2\\u0080\\u0093 The username.\\n\\n  * **permission** \\u00e2\\u0080\\u0093 Permission to grant. Must be one of \\u00e2\\u0080\\u009cREAD\\u00e2\\u0080\\u009d, \\u00e2\\u0080\\u009cEDIT\\u00e2\\u0080\\u009d, \\u00e2\\u0080\\u009cMANAGE\\u00e2\\u0080\\u009d and \\u00e2\\u0080\\u009cNO_PERMISSIONS\\u00e2\\u0080\\u009d.\\n\\nRaises\\n\\n    \\n\\n[**mlflow.exceptions.RestException**](../python_api/exceptions/mlflow.exceptions.html#mlflow.exceptions.RestException\\n\\\"mlflow.exceptions.RestException\\\") \\u00e2\\u0080\\u0093 if the user does not exist, or a\\npermission already exists for this experiment user pair, or if the permission\\nis invalid. Does not require `experiment_id` to be an existing experiment.\\n\\nReturns\\n\\n    \\n\\nA single `mlflow.server.auth.entities.ExperimentPermission` object.\\n\\nExample\\n\\n    \\n    \\n    export MLFLOW_TRACKING_USERNAME=admin\\n    export MLFLOW_TRACKING_PASSWORD=password\\n    \\n    \\n    \\n    from mlflow.server.auth.client import AuthServiceClient\\n    \\n    client = AuthServiceClient(\\\"tracking_uri\\\")\\n    client.create_user(\\\"newuser\\\", \\\"newpassword\\\")\\n    ep = client.create_experiment_permission(\\\"myexperiment\\\", \\\"newuser\\\", \\\"READ\\\")\\n    \\n    print(f\\\"experiment_id: {ep.experiment_id}\\\")\\n    print(f\\\"user_id: {ep.user_id}\\\")\\n    print(f\\\"permission: {ep.permission}\\\")\\n    \\n\\nOutput\\n\\n    \\n    \\n    experiment_id: myexperiment\\n    user_id: 3\\n    permission: READ\\n    \\n\\ncreate_registered_model_permission(_name : str_, _username : str_, _permission\\n:\\nstr_)[[source]](../_modules/mlflow/server/auth/client.html#AuthServiceClient.create_registered_model_permission)\\n\\n    \\n\\nCreate a permission on an registered model for a user.\\n\\nParameters\\n\\n    \\n\\n  * **name** \\u00e2\\u0080\\u0093 The name of the registered model.\\n\\n  * **username** \\u00e2\\u0080\\u0093 The username.\\n\\n  * **permission** \\u00e2\\u0080\\u0093 Permission to grant. Must be one of \\u00e2\\u0080\\u009cREAD\\u00e2\\u0080\\u009d, \\u00e2\\u0080\\u009cEDIT\\u00e2\\u0080\\u009d, \\u00e2\\u0080\\u009cMANAGE\\u00e2\\u0080\\u009d and \\u00e2\\u0080\\u009cNO_PERMISSIONS\\u00e2\\u0080\\u009d.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 1190, \"end_char_idx\": 3185, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/auth/python-api.html","doc_id":"https://mlflow.org/docs/latest/auth/python-api.html","ref_doc_id":"https://mlflow.org/docs/latest/auth/python-api.html"}
{"_node_content":"{\"id_\": \"645ee7b7-e416-4938-9131-575e54a8549f\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/auth/python-api.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"f294cc47573dd5d5fd1fce8fda3f62595b00d1a125103aeee2bc61ffdc3b45d4\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"d0704d9b-cb01-44e0-a98d-e195562d2e60\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"e5b226906777e0b4f8b1d0fc50d02f707ba492af6ef7f9cbb5812a769d078138\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"8dd4aa71-cf1e-400a-92f3-dd881ded50f5\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"324140d7e77eb64fa42dafb56cd9940475ff758deef27c1f0696a636701e750c\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"Parameters\\n\\n    \\n\\n  * **name** \\u00e2\\u0080\\u0093 The name of the registered model.\\n\\n  * **username** \\u00e2\\u0080\\u0093 The username.\\n\\n  * **permission** \\u00e2\\u0080\\u0093 Permission to grant. Must be one of \\u00e2\\u0080\\u009cREAD\\u00e2\\u0080\\u009d, \\u00e2\\u0080\\u009cEDIT\\u00e2\\u0080\\u009d, \\u00e2\\u0080\\u009cMANAGE\\u00e2\\u0080\\u009d and \\u00e2\\u0080\\u009cNO_PERMISSIONS\\u00e2\\u0080\\u009d.\\n\\nRaises\\n\\n    \\n\\n[**mlflow.exceptions.RestException**](../python_api/exceptions/mlflow.exceptions.html#mlflow.exceptions.RestException\\n\\\"mlflow.exceptions.RestException\\\") \\u00e2\\u0080\\u0093 if the user does not exist, or a\\npermission already exists for this registered model user pair, or if the\\npermission is invalid. Does not require `name` to be an existing registered\\nmodel.\\n\\nReturns\\n\\n    \\n\\nA single `mlflow.server.auth.entities.RegisteredModelPermission` object.\\n\\ncreate_user(_username : str_, _password :\\nstr_)[[source]](../_modules/mlflow/server/auth/client.html#AuthServiceClient.create_user)\\n\\n    \\n\\nCreate a new user.\\n\\nParameters\\n\\n    \\n\\n  * **username** \\u00e2\\u0080\\u0093 The username.\\n\\n  * **password** \\u00e2\\u0080\\u0093 The user\\u00e2\\u0080\\u0099s password. Must not be empty string.\\n\\nRaises\\n\\n    \\n\\n[**mlflow.exceptions.RestException**](../python_api/exceptions/mlflow.exceptions.html#mlflow.exceptions.RestException\\n\\\"mlflow.exceptions.RestException\\\") \\u00e2\\u0080\\u0093 if the username is already taken.\\n\\nReturns\\n\\n    \\n\\nA single `mlflow.server.auth.entities.User` object.\\n\\nExample\\n\\n    \\n    \\n    from mlflow.server.auth.client import AuthServiceClient\\n    \\n    client = AuthServiceClient(\\\"tracking_uri\\\")\\n    user = client.create_user(\\\"newuser\\\", \\\"newpassword\\\")\\n    print(f\\\"user_id: {user.id}\\\")\\n    print(f\\\"username: {user.username}\\\")\\n    print(f\\\"password_hash: {user.password_hash}\\\")\\n    print(f\\\"is_admin: {user.is_admin}\\\")\\n    \\n\\nOutput\\n\\n    \\n    \\n    user_id: 3\\n    username: newuser\\n    password_hash: REDACTED\\n    is_admin: False\\n    \\n\\ndelete_experiment_permission(_experiment_id : str_, _username :\\nstr_)[[source]](../_modules/mlflow/server/auth/client.html#AuthServiceClient.delete_experiment_permission)\\n\\n    \\n\\nDelete an existing experiment permission for a user.\\n\\nParameters\\n\\n    \\n\\n  * **experiment_id** \\u00e2\\u0080\\u0093 The id of the experiment.\\n\\n  * **username** \\u00e2\\u0080\\u0093 The username.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 2958, \"end_char_idx\": 4997, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/auth/python-api.html","doc_id":"https://mlflow.org/docs/latest/auth/python-api.html","ref_doc_id":"https://mlflow.org/docs/latest/auth/python-api.html"}
{"_node_content":"{\"id_\": \"8dd4aa71-cf1e-400a-92f3-dd881ded50f5\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/auth/python-api.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"f294cc47573dd5d5fd1fce8fda3f62595b00d1a125103aeee2bc61ffdc3b45d4\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"645ee7b7-e416-4938-9131-575e54a8549f\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"4e4a50c1b79a2bc09f62f28cc5083b5e3f76a7e735cce467205eb414d6f51b48\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"a63df64a-3dd7-4f43-aac2-6b8eee290b88\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"bfab7cba93e75358dc46a9a81b3f0ba50b3f6836bc96558ce092d07a5751364f\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"Returns\\n\\n    \\n\\nA single `mlflow.server.auth.entities.User` object.\\n\\nExample\\n\\n    \\n    \\n    from mlflow.server.auth.client import AuthServiceClient\\n    \\n    client = AuthServiceClient(\\\"tracking_uri\\\")\\n    user = client.create_user(\\\"newuser\\\", \\\"newpassword\\\")\\n    print(f\\\"user_id: {user.id}\\\")\\n    print(f\\\"username: {user.username}\\\")\\n    print(f\\\"password_hash: {user.password_hash}\\\")\\n    print(f\\\"is_admin: {user.is_admin}\\\")\\n    \\n\\nOutput\\n\\n    \\n    \\n    user_id: 3\\n    username: newuser\\n    password_hash: REDACTED\\n    is_admin: False\\n    \\n\\ndelete_experiment_permission(_experiment_id : str_, _username :\\nstr_)[[source]](../_modules/mlflow/server/auth/client.html#AuthServiceClient.delete_experiment_permission)\\n\\n    \\n\\nDelete an existing experiment permission for a user.\\n\\nParameters\\n\\n    \\n\\n  * **experiment_id** \\u00e2\\u0080\\u0093 The id of the experiment.\\n\\n  * **username** \\u00e2\\u0080\\u0093 The username.\\n\\nRaises\\n\\n    \\n\\n[**mlflow.exceptions.RestException**](../python_api/exceptions/mlflow.exceptions.html#mlflow.exceptions.RestException\\n\\\"mlflow.exceptions.RestException\\\") \\u00e2\\u0080\\u0093 if the user does not exist, or no\\npermission exists for this experiment user pair, or if the permission is\\ninvalid. Note that the default permission will still be effective even after\\nthe permission has been deleted.\\n\\nExample\\n\\n    \\n    \\n    export MLFLOW_TRACKING_USERNAME=admin\\n    export MLFLOW_TRACKING_PASSWORD=password\\n    \\n    \\n    \\n    from mlflow.server.auth.client import AuthServiceClient\\n    \\n    client = AuthServiceClient(\\\"tracking_uri\\\")\\n    client.create_user(\\\"newuser\\\", \\\"newpassword\\\")\\n    client.create_experiment_permission(\\\"myexperiment\\\", \\\"newuser\\\", \\\"READ\\\")\\n    client.delete_experiment_permission(\\\"myexperiment\\\", \\\"newuser\\\")\\n    \\n\\ndelete_registered_model_permission(_name : str_, _username :\\nstr_)[[source]](../_modules/mlflow/server/auth/client.html#AuthServiceClient.delete_registered_model_permission)\\n\\n    \\n\\nDelete an existing registered model permission for a user.\\n\\nParameters\\n\\n    \\n\\n  * **name** \\u00e2\\u0080\\u0093 The name of the registered model.\\n\\n  * **username** \\u00e2\\u0080\\u0093 The username.\\n\\nRaises\\n\\n    \\n\\n[**mlflow.exceptions.RestException**](../python_api/exceptions/mlflow.exceptions.html#mlflow.exceptions.RestException\\n\\\"mlflow.exceptions.RestException\\\") \\u00e2\\u0080\\u0093 if the user does not exist, or no\\npermission exists for this registered model user pair, or if the permission is\\ninvalid.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 4127, \"end_char_idx\": 6457, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/auth/python-api.html","doc_id":"https://mlflow.org/docs/latest/auth/python-api.html","ref_doc_id":"https://mlflow.org/docs/latest/auth/python-api.html"}
{"_node_content":"{\"id_\": \"a63df64a-3dd7-4f43-aac2-6b8eee290b88\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/auth/python-api.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"f294cc47573dd5d5fd1fce8fda3f62595b00d1a125103aeee2bc61ffdc3b45d4\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"8dd4aa71-cf1e-400a-92f3-dd881ded50f5\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"324140d7e77eb64fa42dafb56cd9940475ff758deef27c1f0696a636701e750c\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"7a2ca049-6d46-42c8-8747-c4742a234e64\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"77b94f5c702ecdf4f7c44b73c6a6aa7c0665ca29f2ec4aebe5a3c5075f29d79f\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"Parameters\\n\\n    \\n\\n  * **name** \\u00e2\\u0080\\u0093 The name of the registered model.\\n\\n  * **username** \\u00e2\\u0080\\u0093 The username.\\n\\nRaises\\n\\n    \\n\\n[**mlflow.exceptions.RestException**](../python_api/exceptions/mlflow.exceptions.html#mlflow.exceptions.RestException\\n\\\"mlflow.exceptions.RestException\\\") \\u00e2\\u0080\\u0093 if the user does not exist, or no\\npermission exists for this registered model user pair, or if the permission is\\ninvalid. Note that the default permission will still be effective even after\\nthe permission has been deleted.\\n\\nExample\\n\\n    \\n    \\n    export MLFLOW_TRACKING_USERNAME=admin\\n    export MLFLOW_TRACKING_PASSWORD=password\\n    \\n    \\n    \\n    from mlflow.server.auth.client import AuthServiceClient\\n    \\n    client = AuthServiceClient(\\\"tracking_uri\\\")\\n    client.create_user(\\\"newuser\\\", \\\"newpassword\\\")\\n    client.create_registered_model_permission(\\\"myregisteredmodel\\\", \\\"newuser\\\", \\\"READ\\\")\\n    client.delete_registered_model_permission(\\\"myregisteredmodel\\\", \\\"newuser\\\")\\n    \\n\\ndelete_user(_username :\\nstr_)[[source]](../_modules/mlflow/server/auth/client.html#AuthServiceClient.delete_user)\\n\\n    \\n\\nDelete a specific user.\\n\\nParameters\\n\\n    \\n\\n**username** \\u00e2\\u0080\\u0093 The username.\\n\\nRaises\\n\\n    \\n\\n[**mlflow.exceptions.RestException**](../python_api/exceptions/mlflow.exceptions.html#mlflow.exceptions.RestException\\n\\\"mlflow.exceptions.RestException\\\") \\u00e2\\u0080\\u0093 if the user does not exist\\n\\nExample\\n\\n    \\n    \\n    export MLFLOW_TRACKING_USERNAME=admin\\n    export MLFLOW_TRACKING_PASSWORD=password\\n    \\n    \\n    \\n    from mlflow.server.auth.client import AuthServiceClient\\n    \\n    client = AuthServiceClient(\\\"tracking_uri\\\")\\n    client.create_user(\\\"newuser\\\", \\\"newpassword\\\")\\n    \\n    client.delete_user(\\\"newuser\\\")\\n    \\n\\nget_experiment_permission(_experiment_id : str_, _username :\\nstr_)[[source]](../_modules/mlflow/server/auth/client.html#AuthServiceClient.get_experiment_permission)\\n\\n    \\n\\nGet an experiment permission for a user.\\n\\nParameters\\n\\n    \\n\\n  * **experiment_id** \\u00e2\\u0080\\u0093 The id of the experiment.\\n\\n  * **username** \\u00e2\\u0080\\u0093 The username.\\n\\nRaises\\n\\n    \\n\\n[**mlflow.exceptions.RestException**](../python_api/exceptions/mlflow.exceptions.html#mlflow.exceptions.RestException\\n\\\"mlflow.exceptions.RestException\\\") \\u00e2\\u0080\\u0093 if the user does not exist, or no\\npermission exists for this experiment user pair. Note that the default\\npermission will still be effective even if no permission exists.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 6059, \"end_char_idx\": 8395, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/auth/python-api.html","doc_id":"https://mlflow.org/docs/latest/auth/python-api.html","ref_doc_id":"https://mlflow.org/docs/latest/auth/python-api.html"}
{"_node_content":"{\"id_\": \"7a2ca049-6d46-42c8-8747-c4742a234e64\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/auth/python-api.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"f294cc47573dd5d5fd1fce8fda3f62595b00d1a125103aeee2bc61ffdc3b45d4\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"a63df64a-3dd7-4f43-aac2-6b8eee290b88\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"bfab7cba93e75358dc46a9a81b3f0ba50b3f6836bc96558ce092d07a5751364f\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"2c018382-1b6a-4173-a4e6-cb294a5736d3\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"5039d1d31dd91b344e7ce622bde7d03458325b1137968f96e041fcd07654e828\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"Parameters\\n\\n    \\n\\n  * **experiment_id** \\u00e2\\u0080\\u0093 The id of the experiment.\\n\\n  * **username** \\u00e2\\u0080\\u0093 The username.\\n\\nRaises\\n\\n    \\n\\n[**mlflow.exceptions.RestException**](../python_api/exceptions/mlflow.exceptions.html#mlflow.exceptions.RestException\\n\\\"mlflow.exceptions.RestException\\\") \\u00e2\\u0080\\u0093 if the user does not exist, or no\\npermission exists for this experiment user pair. Note that the default\\npermission will still be effective even if no permission exists.\\n\\nReturns\\n\\n    \\n\\nA single `mlflow.server.auth.entities.ExperimentPermission` object.\\n\\nExample\\n\\n    \\n    \\n    export MLFLOW_TRACKING_USERNAME=admin\\n    export MLFLOW_TRACKING_PASSWORD=password\\n    \\n    \\n    \\n    from mlflow.server.auth.client import AuthServiceClient\\n    \\n    client = AuthServiceClient(\\\"tracking_uri\\\")\\n    client.create_user(\\\"newuser\\\", \\\"newpassword\\\")\\n    client.create_experiment_permission(\\\"myexperiment\\\", \\\"newuser\\\", \\\"READ\\\")\\n    ep = client.get_experiment_permission(\\\"myexperiment\\\", \\\"newuser\\\")\\n    print(f\\\"experiment_id: {ep.experiment_id}\\\")\\n    print(f\\\"user_id: {ep.user_id}\\\")\\n    print(f\\\"permission: {ep.permission}\\\")\\n    \\n\\nOutput\\n\\n    \\n    \\n    experiment_id: myexperiment\\n    user_id: 3\\n    permission: READ\\n    \\n\\nget_registered_model_permission(_name : str_, _username :\\nstr_)[[source]](../_modules/mlflow/server/auth/client.html#AuthServiceClient.get_registered_model_permission)\\n\\n    \\n\\nGet an registered model permission for a user.\\n\\nParameters\\n\\n    \\n\\n  * **name** \\u00e2\\u0080\\u0093 The name of the registered model.\\n\\n  * **username** \\u00e2\\u0080\\u0093 The username.\\n\\nRaises\\n\\n    \\n\\n[**mlflow.exceptions.RestException**](../python_api/exceptions/mlflow.exceptions.html#mlflow.exceptions.RestException\\n\\\"mlflow.exceptions.RestException\\\") \\u00e2\\u0080\\u0093 if the user does not exist, or no\\npermission exists for this registered model user pair. Note that the default\\npermission will still be effective even if no permission exists.\\n\\nReturns\\n\\n    \\n\\nA single `mlflow.server.auth.entities.RegisteredModelPermission` object.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 7948, \"end_char_idx\": 9895, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/auth/python-api.html","doc_id":"https://mlflow.org/docs/latest/auth/python-api.html","ref_doc_id":"https://mlflow.org/docs/latest/auth/python-api.html"}
{"_node_content":"{\"id_\": \"2c018382-1b6a-4173-a4e6-cb294a5736d3\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/auth/python-api.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"f294cc47573dd5d5fd1fce8fda3f62595b00d1a125103aeee2bc61ffdc3b45d4\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"7a2ca049-6d46-42c8-8747-c4742a234e64\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"77b94f5c702ecdf4f7c44b73c6a6aa7c0665ca29f2ec4aebe5a3c5075f29d79f\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"01f2ba8a-0219-447f-a2a3-dedfb96360e3\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"88991b308f2147fdc141ff92133601ec9decbdb412e832a2c6852d91ca70b5f9\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"Parameters\\n\\n    \\n\\n  * **name** \\u00e2\\u0080\\u0093 The name of the registered model.\\n\\n  * **username** \\u00e2\\u0080\\u0093 The username.\\n\\nRaises\\n\\n    \\n\\n[**mlflow.exceptions.RestException**](../python_api/exceptions/mlflow.exceptions.html#mlflow.exceptions.RestException\\n\\\"mlflow.exceptions.RestException\\\") \\u00e2\\u0080\\u0093 if the user does not exist, or no\\npermission exists for this registered model user pair. Note that the default\\npermission will still be effective even if no permission exists.\\n\\nReturns\\n\\n    \\n\\nA single `mlflow.server.auth.entities.RegisteredModelPermission` object.\\n\\nExample\\n\\n    \\n    \\n    export MLFLOW_TRACKING_USERNAME=admin\\n    export MLFLOW_TRACKING_PASSWORD=password\\n    \\n    \\n    \\n    from mlflow.server.auth.client import AuthServiceClient\\n    \\n    client = AuthServiceClient(\\\"tracking_uri\\\")\\n    client.create_user(\\\"newuser\\\", \\\"newpassword\\\")\\n    client.create_registered_model_permission(\\\"myregisteredmodel\\\", \\\"newuser\\\", \\\"READ\\\")\\n    rmp = client.get_registered_model_permission(\\\"myregisteredmodel\\\", \\\"newuser\\\")\\n    \\n    print(f\\\"name: {rmp.name}\\\")\\n    print(f\\\"user_id: {rmp.user_id}\\\")\\n    print(f\\\"permission: {rmp.permission}\\\")\\n    \\n\\nOutput\\n\\n    \\n    \\n    name: myregisteredmodel\\n    user_id: 3\\n    permission: READ\\n    \\n\\nget_user(_username :\\nstr_)[[source]](../_modules/mlflow/server/auth/client.html#AuthServiceClient.get_user)\\n\\n    \\n\\nGet a user with a specific username.\\n\\nParameters\\n\\n    \\n\\n**username** \\u00e2\\u0080\\u0093 The username.\\n\\nRaises\\n\\n    \\n\\n[**mlflow.exceptions.RestException**](../python_api/exceptions/mlflow.exceptions.html#mlflow.exceptions.RestException\\n\\\"mlflow.exceptions.RestException\\\") \\u00e2\\u0080\\u0093 if the user does not exist\\n\\nReturns\\n\\n    \\n\\nA single `mlflow.server.auth.entities.User` object.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 9354, \"end_char_idx\": 11024, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/auth/python-api.html","doc_id":"https://mlflow.org/docs/latest/auth/python-api.html","ref_doc_id":"https://mlflow.org/docs/latest/auth/python-api.html"}
{"_node_content":"{\"id_\": \"01f2ba8a-0219-447f-a2a3-dedfb96360e3\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/auth/python-api.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"f294cc47573dd5d5fd1fce8fda3f62595b00d1a125103aeee2bc61ffdc3b45d4\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"2c018382-1b6a-4173-a4e6-cb294a5736d3\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"5039d1d31dd91b344e7ce622bde7d03458325b1137968f96e041fcd07654e828\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"fa1ed9ed-57f5-49bd-a8fc-8206a69f5d19\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"523a238f4281b5db9dd694b268304bdd55c43154b155dc57c3b77de3d0118edf\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"Parameters\\n\\n    \\n\\n**username** \\u00e2\\u0080\\u0093 The username.\\n\\nRaises\\n\\n    \\n\\n[**mlflow.exceptions.RestException**](../python_api/exceptions/mlflow.exceptions.html#mlflow.exceptions.RestException\\n\\\"mlflow.exceptions.RestException\\\") \\u00e2\\u0080\\u0093 if the user does not exist\\n\\nReturns\\n\\n    \\n\\nA single `mlflow.server.auth.entities.User` object.\\n\\nExample\\n\\n    \\n    \\n    export MLFLOW_TRACKING_USERNAME=admin\\n    export MLFLOW_TRACKING_PASSWORD=password\\n    \\n    \\n    \\n    from mlflow.server.auth.client import AuthServiceClient\\n    \\n    client = AuthServiceClient(\\\"tracking_uri\\\")\\n    client.create_user(\\\"newuser\\\", \\\"newpassword\\\")\\n    user = client.get_user(\\\"newuser\\\")\\n    \\n    print(f\\\"user_id: {user.id}\\\")\\n    print(f\\\"username: {user.username}\\\")\\n    print(f\\\"password_hash: {user.password_hash}\\\")\\n    print(f\\\"is_admin: {user.is_admin}\\\")\\n    \\n\\nOutput\\n\\n    \\n    \\n    user_id: 3\\n    username: newuser\\n    password_hash: REDACTED\\n    is_admin: False\\n    \\n\\nupdate_experiment_permission(_experiment_id : str_, _username : str_,\\n_permission :\\nstr_)[[source]](../_modules/mlflow/server/auth/client.html#AuthServiceClient.update_experiment_permission)\\n\\n    \\n\\nUpdate an existing experiment permission for a user.\\n\\nParameters\\n\\n    \\n\\n  * **experiment_id** \\u00e2\\u0080\\u0093 The id of the experiment.\\n\\n  * **username** \\u00e2\\u0080\\u0093 The username.\\n\\n  * **permission** \\u00e2\\u0080\\u0093 New permission to grant. Must be one of \\u00e2\\u0080\\u009cREAD\\u00e2\\u0080\\u009d, \\u00e2\\u0080\\u009cEDIT\\u00e2\\u0080\\u009d, \\u00e2\\u0080\\u009cMANAGE\\u00e2\\u0080\\u009d and \\u00e2\\u0080\\u009cNO_PERMISSIONS\\u00e2\\u0080\\u009d.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 10709, \"end_char_idx\": 12113, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/auth/python-api.html","doc_id":"https://mlflow.org/docs/latest/auth/python-api.html","ref_doc_id":"https://mlflow.org/docs/latest/auth/python-api.html"}
{"_node_content":"{\"id_\": \"fa1ed9ed-57f5-49bd-a8fc-8206a69f5d19\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/auth/python-api.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"f294cc47573dd5d5fd1fce8fda3f62595b00d1a125103aeee2bc61ffdc3b45d4\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"01f2ba8a-0219-447f-a2a3-dedfb96360e3\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"88991b308f2147fdc141ff92133601ec9decbdb412e832a2c6852d91ca70b5f9\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"d9adf8c1-6ca0-437b-a5ee-669738ebe5ca\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"9f16277ddeff46f9d03fdb35015d617d1eea86dde7633c3b68264e5c3e5944fc\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"Parameters\\n\\n    \\n\\n  * **experiment_id** \\u00e2\\u0080\\u0093 The id of the experiment.\\n\\n  * **username** \\u00e2\\u0080\\u0093 The username.\\n\\n  * **permission** \\u00e2\\u0080\\u0093 New permission to grant. Must be one of \\u00e2\\u0080\\u009cREAD\\u00e2\\u0080\\u009d, \\u00e2\\u0080\\u009cEDIT\\u00e2\\u0080\\u009d, \\u00e2\\u0080\\u009cMANAGE\\u00e2\\u0080\\u009d and \\u00e2\\u0080\\u009cNO_PERMISSIONS\\u00e2\\u0080\\u009d.\\n\\nRaises\\n\\n    \\n\\n[**mlflow.exceptions.RestException**](../python_api/exceptions/mlflow.exceptions.html#mlflow.exceptions.RestException\\n\\\"mlflow.exceptions.RestException\\\") \\u00e2\\u0080\\u0093 if the user does not exist, or no\\npermission exists for this experiment user pair, or if the permission is\\ninvalid\\n\\nExample\\n\\n    \\n    \\n    export MLFLOW_TRACKING_USERNAME=admin\\n    export MLFLOW_TRACKING_PASSWORD=password\\n    \\n    \\n    \\n    from mlflow.server.auth.client import AuthServiceClient\\n    \\n    client = AuthServiceClient(\\\"tracking_uri\\\")\\n    client.create_user(\\\"newuser\\\", \\\"newpassword\\\")\\n    client.create_experiment_permission(\\\"myexperiment\\\", \\\"newuser\\\", \\\"READ\\\")\\n    client.update_experiment_permission(\\\"myexperiment\\\", \\\"newuser\\\", \\\"EDIT\\\")\\n    \\n\\nupdate_registered_model_permission(_name : str_, _username : str_, _permission\\n:\\nstr_)[[source]](../_modules/mlflow/server/auth/client.html#AuthServiceClient.update_registered_model_permission)\\n\\n    \\n\\nUpdate an existing registered model permission for a user.\\n\\nParameters\\n\\n    \\n\\n  * **name** \\u00e2\\u0080\\u0093 The name of the registered model.\\n\\n  * **username** \\u00e2\\u0080\\u0093 The username.\\n\\n  * **permission** \\u00e2\\u0080\\u0093 New permission to grant. Must be one of \\u00e2\\u0080\\u009cREAD\\u00e2\\u0080\\u009d, \\u00e2\\u0080\\u009cEDIT\\u00e2\\u0080\\u009d, \\u00e2\\u0080\\u009cMANAGE\\u00e2\\u0080\\u009d and \\u00e2\\u0080\\u009cNO_PERMISSIONS\\u00e2\\u0080\\u009d.\\n\\nRaises\\n\\n    \\n\\n[**mlflow.exceptions.RestException**](../python_api/exceptions/mlflow.exceptions.html#mlflow.exceptions.RestException\\n\\\"mlflow.exceptions.RestException\\\") \\u00e2\\u0080\\u0093 if the user does not exist, or no\\npermission exists for this registered model user pair, or if the permission is\\ninvalid.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 11881, \"end_char_idx\": 13626, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/auth/python-api.html","doc_id":"https://mlflow.org/docs/latest/auth/python-api.html","ref_doc_id":"https://mlflow.org/docs/latest/auth/python-api.html"}
{"_node_content":"{\"id_\": \"d9adf8c1-6ca0-437b-a5ee-669738ebe5ca\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/auth/python-api.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"f294cc47573dd5d5fd1fce8fda3f62595b00d1a125103aeee2bc61ffdc3b45d4\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"fa1ed9ed-57f5-49bd-a8fc-8206a69f5d19\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"523a238f4281b5db9dd694b268304bdd55c43154b155dc57c3b77de3d0118edf\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"3a080226-3cd2-47da-93d7-e4378785b7b5\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"1d7b52a8b5235f6151c4713fecd52f4855dfe15b94b711142343168dc33318d3\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"Parameters\\n\\n    \\n\\n  * **name** \\u00e2\\u0080\\u0093 The name of the registered model.\\n\\n  * **username** \\u00e2\\u0080\\u0093 The username.\\n\\n  * **permission** \\u00e2\\u0080\\u0093 New permission to grant. Must be one of \\u00e2\\u0080\\u009cREAD\\u00e2\\u0080\\u009d, \\u00e2\\u0080\\u009cEDIT\\u00e2\\u0080\\u009d, \\u00e2\\u0080\\u009cMANAGE\\u00e2\\u0080\\u009d and \\u00e2\\u0080\\u009cNO_PERMISSIONS\\u00e2\\u0080\\u009d.\\n\\nRaises\\n\\n    \\n\\n[**mlflow.exceptions.RestException**](../python_api/exceptions/mlflow.exceptions.html#mlflow.exceptions.RestException\\n\\\"mlflow.exceptions.RestException\\\") \\u00e2\\u0080\\u0093 if the user does not exist, or no\\npermission exists for this registered model user pair, or if the permission is\\ninvalid.\\n\\nExample\\n\\n    \\n    \\n    export MLFLOW_TRACKING_USERNAME=admin\\n    export MLFLOW_TRACKING_PASSWORD=password\\n    \\n    \\n    \\n    from mlflow.server.auth.client import AuthServiceClient\\n    \\n    client = AuthServiceClient(\\\"tracking_uri\\\")\\n    client.create_user(\\\"newuser\\\", \\\"newpassword\\\")\\n    client.create_registered_model_permission(\\\"myregisteredmodel\\\", \\\"newuser\\\", \\\"READ\\\")\\n    client.update_registered_model_permission(\\\"myregisteredmodel\\\", \\\"newuser\\\", \\\"EDIT\\\")\\n    \\n\\nupdate_user_admin(_username : str_, _is_admin :\\nbool_)[[source]](../_modules/mlflow/server/auth/client.html#AuthServiceClient.update_user_admin)\\n\\n    \\n\\nUpdate the admin status of a specific user.\\n\\nParameters\\n\\n    \\n\\n  * **username** \\u00e2\\u0080\\u0093 The username.\\n\\n  * **is_admin** \\u00e2\\u0080\\u0093 The new admin status.\\n\\nRaises\\n\\n    \\n\\n[**mlflow.exceptions.RestException**](../python_api/exceptions/mlflow.exceptions.html#mlflow.exceptions.RestException\\n\\\"mlflow.exceptions.RestException\\\") \\u00e2\\u0080\\u0093 if the user does not exist\\n\\nExample\\n\\n    \\n    \\n    export MLFLOW_TRACKING_USERNAME=admin\\n    export MLFLOW_TRACKING_PASSWORD=password\\n    \\n    \\n    \\n    from mlflow.server.auth.client import AuthServiceClient\\n    \\n    client = AuthServiceClient(\\\"tracking_uri\\\")\\n    client.create_user(\\\"newuser\\\", \\\"newpassword\\\")\\n    \\n    client.update_user_admin(\\\"newuser\\\", True)\\n    \\n\\nupdate_user_password(_username : str_, _password :\\nstr_)[[source]](../_modules/mlflow/server/auth/client.html#AuthServiceClient.update_user_password)\\n\\n    \\n\\nUpdate the password of a specific user.\\n\\nParameters\\n\\n    \\n\\n  * **username** \\u00e2\\u0080\\u0093 The username.\\n\\n  * **password** \\u00e2\\u0080\\u0093 The new password.\\n\\nRaises\\n\\n    \\n\\n[**mlflow.exceptions.RestException**](.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 13101, \"end_char_idx\": 15266, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/auth/python-api.html","doc_id":"https://mlflow.org/docs/latest/auth/python-api.html","ref_doc_id":"https://mlflow.org/docs/latest/auth/python-api.html"}
{"_node_content":"{\"id_\": \"3a080226-3cd2-47da-93d7-e4378785b7b5\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/auth/python-api.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"f294cc47573dd5d5fd1fce8fda3f62595b00d1a125103aeee2bc61ffdc3b45d4\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"d9adf8c1-6ca0-437b-a5ee-669738ebe5ca\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"9f16277ddeff46f9d03fdb35015d617d1eea86dde7633c3b68264e5c3e5944fc\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"86f7d3e8-17a4-4273-adfb-a39b5aa8e716\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"47be869faa6f0540390bc0dcbc94f182b0a2518ac806385af416bf734c1467de\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"Raises\\n\\n    \\n\\n[**mlflow.exceptions.RestException**](../python_api/exceptions/mlflow.exceptions.html#mlflow.exceptions.RestException\\n\\\"mlflow.exceptions.RestException\\\") \\u00e2\\u0080\\u0093 if the user does not exist\\n\\nExample\\n\\n    \\n    \\n    export MLFLOW_TRACKING_USERNAME=admin\\n    export MLFLOW_TRACKING_PASSWORD=password\\n    \\n    \\n    \\n    from mlflow.server.auth.client import AuthServiceClient\\n    \\n    client = AuthServiceClient(\\\"tracking_uri\\\")\\n    client.create_user(\\\"newuser\\\", \\\"newpassword\\\")\\n    \\n    client.update_user_admin(\\\"newuser\\\", True)\\n    \\n\\nupdate_user_password(_username : str_, _password :\\nstr_)[[source]](../_modules/mlflow/server/auth/client.html#AuthServiceClient.update_user_password)\\n\\n    \\n\\nUpdate the password of a specific user.\\n\\nParameters\\n\\n    \\n\\n  * **username** \\u00e2\\u0080\\u0093 The username.\\n\\n  * **password** \\u00e2\\u0080\\u0093 The new password.\\n\\nRaises\\n\\n    \\n\\n[**mlflow.exceptions.RestException**](./python_api/exceptions/mlflow.exceptions.html#mlflow.exceptions.RestException\\n\\\"mlflow.exceptions.RestException\\\") \\u00e2\\u0080\\u0093 if the user does not exist\\n\\nExample\\n\\n    \\n    \\n    export MLFLOW_TRACKING_USERNAME=admin\\n    export MLFLOW_TRACKING_PASSWORD=password\\n    \\n    \\n    \\n    from mlflow.server.auth.client import AuthServiceClient\\n    \\n    client = AuthServiceClient(\\\"tracking_uri\\\")\\n    client.create_user(\\\"newuser\\\", \\\"newpassword\\\")\\n    \\n    client.update_user_password(\\\"newuser\\\", \\\"anotherpassword\\\")\\n    \\n\\n## mlflow.server.auth.entities\\n\\n_class _mlflow.server.auth.entities.ExperimentPermission(_experiment_id_ ,\\n_user_id_ ,\\n_permission_)[[source]](./_modules/mlflow/server/auth/entities.html#ExperimentPermission)\\n\\n    \\n\\nBases: `object`\\n\\n_property _experiment_id\\n\\n    \\n\\n_classmethod\\n_from_json(_dictionary_)[[source]](./_modules/mlflow/server/auth/entities.html#ExperimentPermission.from_json)\\n\\n    \\n\\n_property _permission\\n\\n    \\n\\nto_json()[[source]](./_modules/mlflow/server/auth/entities.html#ExperimentPermission.to_json)\\n\\n    \\n\\n_property _user_id\\n\\n    \\n\\n_class _mlflow.server.auth.entities.RegisteredModelPermission(_name_ ,\\n_user_id_ ,\\n_permission_)[[source]](./_modules/mlflow/server/auth/entities.html#RegisteredModelPermission)\\n\\n    \\n\\nBases: `object`\\n\\n_classmethod\\n_from_json(_dictionary_)[[source]](.\", \"mimetype\": \"text/plain\", \"start_char_idx\": null, \"end_char_idx\": null, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/auth/python-api.html","doc_id":"https://mlflow.org/docs/latest/auth/python-api.html","ref_doc_id":"https://mlflow.org/docs/latest/auth/python-api.html"}
{"_node_content":"{\"id_\": \"86f7d3e8-17a4-4273-adfb-a39b5aa8e716\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/auth/python-api.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"f294cc47573dd5d5fd1fce8fda3f62595b00d1a125103aeee2bc61ffdc3b45d4\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"3a080226-3cd2-47da-93d7-e4378785b7b5\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"1d7b52a8b5235f6151c4713fecd52f4855dfe15b94b711142343168dc33318d3\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"server.auth.entities\\n\\n_class _mlflow.server.auth.entities.ExperimentPermission(_experiment_id_ ,\\n_user_id_ ,\\n_permission_)[[source]](./_modules/mlflow/server/auth/entities.html#ExperimentPermission)\\n\\n    \\n\\nBases: `object`\\n\\n_property _experiment_id\\n\\n    \\n\\n_classmethod\\n_from_json(_dictionary_)[[source]](./_modules/mlflow/server/auth/entities.html#ExperimentPermission.from_json)\\n\\n    \\n\\n_property _permission\\n\\n    \\n\\nto_json()[[source]](./_modules/mlflow/server/auth/entities.html#ExperimentPermission.to_json)\\n\\n    \\n\\n_property _user_id\\n\\n    \\n\\n_class _mlflow.server.auth.entities.RegisteredModelPermission(_name_ ,\\n_user_id_ ,\\n_permission_)[[source]](./_modules/mlflow/server/auth/entities.html#RegisteredModelPermission)\\n\\n    \\n\\nBases: `object`\\n\\n_classmethod\\n_from_json(_dictionary_)[[source]](./_modules/mlflow/server/auth/entities.html#RegisteredModelPermission.from_json)\\n\\n    \\n\\n_property _name\\n\\n    \\n\\n_property _permission\\n\\n    \\n\\nto_json()[[source]](./_modules/mlflow/server/auth/entities.html#RegisteredModelPermission.to_json)\\n\\n    \\n\\n_property _user_id\\n\\n    \\n\\n_class _mlflow.server.auth.entities.User(_id__ , _username_ , _password_hash_\\n, _is_admin_ , _experiment_permissions =None_, _registered_model_permissions\\n=None_)[[source]](./_modules/mlflow/server/auth/entities.html#User)\\n\\n    \\n\\nBases: `object`\\n\\n_property _experiment_permissions\\n\\n    \\n\\n_classmethod\\n_from_json(_dictionary_)[[source]](./_modules/mlflow/server/auth/entities.html#User.from_json)\\n\\n    \\n\\n_property _id\\n\\n    \\n\\n_property _is_admin\\n\\n    \\n\\n_property _password_hash\\n\\n    \\n\\n_property _registered_model_permissions\\n\\n    \\n\\nto_json()[[source]](./_modules/mlflow/server/auth/entities.html#User.to_json)\\n\\n    \\n\\n_property _username\\n\\n    \\n\\n[ Previous](./cli.html \\\"Command-Line Interface\\\") [Next ](rest-api.html\\n\\\"MLflow Authentication REST API\\\")\\n\\n* * *\\n\\n(C) MLflow Project, a Series of LF Projects, LLC. All rights reserved.\", \"mimetype\": \"text/plain\", \"start_char_idx\": null, \"end_char_idx\": null, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/auth/python-api.html","doc_id":"https://mlflow.org/docs/latest/auth/python-api.html","ref_doc_id":"https://mlflow.org/docs/latest/auth/python-api.html"}
{"_node_content":"{\"id_\": \"4f5692d1-c27a-4712-85c1-80c6a24749cd\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/cli.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"2a8ac9b19cbddee491423c178812937f540f6dfd863499d20a2eb0461839b388\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"f60914cc-f273-4368-9a00-60b13b57061c\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"eccbf80ff5cee407d17bbba768577f11078a394caa97ed3385f5b5480db97e6e\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"__[ ![MLflow](_static/MLflow-logo-final-black.png) ](index.html) [Main\\nDocs](/docs/latest) API Documentation\\n\\n3.1.0\\n\\n[Home](index.html)\\n\\n  * [Python API](python_api/index.html)\\n  * Command-Line Interface\\n    * mlflow\\n      * artifacts\\n        * download\\n        * list\\n        * log-artifact\\n        * log-artifacts\\n      * db\\n        * upgrade\\n      * deployments\\n        * create\\n        * create-endpoint\\n        * delete\\n        * delete-endpoint\\n        * explain\\n        * get\\n        * get-endpoint\\n        * help\\n        * list\\n        * list-endpoints\\n        * predict\\n        * run-local\\n        * update\\n        * update-endpoint\\n      * doctor\\n      * experiments\\n        * create\\n        * csv\\n        * delete\\n        * rename\\n        * restore\\n        * search\\n      * gateway\\n        * start\\n      * gc\\n      * models\\n        * build-docker\\n        * generate-dockerfile\\n        * predict\\n        * prepare-env\\n        * serve\\n        * update-pip-requirements\\n      * run\\n      * runs\\n        * delete\\n        * describe\\n        * list\\n        * restore\\n      * sagemaker\\n        * build-and-push-container\\n        * deploy-transform-job\\n        * push-model\\n        * terminate-transform-job\\n      * server\\n  * [MLflow Authentication Python API](auth/python-api.html)\\n  * [MLflow Authentication REST API](auth/rest-api.html)\\n  * [R API](R-api.html)\\n  * [Java API](java_api/index.html)\\n  * [REST API](rest-api.html)\\n\\n[Contribute](https://github.com/mlflow/mlflow/blob/master/CONTRIBUTING.md)\\n\\n  * [Documentation](index.html)\\n  * Command-Line Interface\\n\\n# Command-Line Interface\\n\\nThe MLflow command-line interface (CLI) provides a simple interface to various\\nfunctionality in MLflow.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 0, \"end_char_idx\": 1700, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/cli.html","doc_id":"https://mlflow.org/docs/latest/cli.html","ref_doc_id":"https://mlflow.org/docs/latest/cli.html"}
{"_node_content":"{\"id_\": \"f60914cc-f273-4368-9a00-60b13b57061c\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/cli.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"2a8ac9b19cbddee491423c178812937f540f6dfd863499d20a2eb0461839b388\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"4f5692d1-c27a-4712-85c1-80c6a24749cd\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"f93ea737fa5f0c23504319ff6e41e53005a8b475c0ddbfd7046e7fe539a5de90\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"86317049-3e45-4f1f-894a-43939eb1cfce\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"bb463d140776457e21378c62acb12056d5c01134859a46fd309de7be354223c7\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"You can use the CLI to run projects, start the\\ntracking UI, create and list experiments, download run artifacts, serve MLflow\\nPython Function and scikit-learn models, serve MLflow Python Function and\\nscikit-learn models, and serve models on [Microsoft Azure Machine\\nLearning](https://azure.microsoft.com/en-us/services/machine-learning-\\nservice/) and [Amazon SageMaker](https://aws.amazon.com/sagemaker/).\\n\\nEach individual command has a detailed help screen accessible via `mlflow\\ncommand_name --help`.\\n\\nAttention\\n\\nIt is advisable to set the `MLFLOW_TRACKING_URI` environment variable by\\ndefault, as the CLI does not automatically connect to a tracking server.\\nWithout this, the CLI will default to using the local filesystem where the\\ncommand is executed, rather than connecting to a localhost or remote HTTP\\nserver. Setting `MLFLOW_TRACKING_URI` to the URL of your desired tracking\\nserver is required for most of the commands below.\\n\\nTable of Contents\\n\\n  * mlflow\\n\\n    * artifacts\\n\\n    * db\\n\\n    * deployments\\n\\n    * doctor\\n\\n    * experiments\\n\\n    * gateway\\n\\n    * gc\\n\\n    * models\\n\\n    * run\\n\\n    * runs\\n\\n    * sagemaker\\n\\n    * server\\n\\n## mlflow\\n\\n    \\n    \\n    mlflow [OPTIONS] COMMAND [ARGS]...\\n    \\n\\nOptions\\n\\n\\\\--version\\n\\n    \\n\\nShow the version and exit.\\n\\n### artifacts\\n\\nUpload, list, and download artifacts from an MLflow artifact repository.\\n\\nTo manage artifacts for a run associated with a tracking server, set the\\nMLFLOW_TRACKING_URI environment variable to the URL of the desired server.\\n\\n    \\n    \\n    mlflow artifacts [OPTIONS] COMMAND [ARGS]...\\n    \\n\\n#### download\\n\\nDownload an artifact file or directory to a local directory. The output is the\\nname of the file or directory on the local filesystem.\\n\\nEither `--artifact-uri` or `--run-id` must be provided.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 1701, \"end_char_idx\": 3469, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/cli.html","doc_id":"https://mlflow.org/docs/latest/cli.html","ref_doc_id":"https://mlflow.org/docs/latest/cli.html"}
{"_node_content":"{\"id_\": \"86317049-3e45-4f1f-894a-43939eb1cfce\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/cli.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"2a8ac9b19cbddee491423c178812937f540f6dfd863499d20a2eb0461839b388\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"f60914cc-f273-4368-9a00-60b13b57061c\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"eccbf80ff5cee407d17bbba768577f11078a394caa97ed3385f5b5480db97e6e\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"58afd1e8-9617-441f-9611-ca1cebb32fce\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"a551fff927cf103e8961bb4e3c0be1aef69bcbf90b1880b0622bef0d05325f02\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"Table of Contents\\n\\n  * mlflow\\n\\n    * artifacts\\n\\n    * db\\n\\n    * deployments\\n\\n    * doctor\\n\\n    * experiments\\n\\n    * gateway\\n\\n    * gc\\n\\n    * models\\n\\n    * run\\n\\n    * runs\\n\\n    * sagemaker\\n\\n    * server\\n\\n## mlflow\\n\\n    \\n    \\n    mlflow [OPTIONS] COMMAND [ARGS]...\\n    \\n\\nOptions\\n\\n\\\\--version\\n\\n    \\n\\nShow the version and exit.\\n\\n### artifacts\\n\\nUpload, list, and download artifacts from an MLflow artifact repository.\\n\\nTo manage artifacts for a run associated with a tracking server, set the\\nMLFLOW_TRACKING_URI environment variable to the URL of the desired server.\\n\\n    \\n    \\n    mlflow artifacts [OPTIONS] COMMAND [ARGS]...\\n    \\n\\n#### download\\n\\nDownload an artifact file or directory to a local directory. The output is the\\nname of the file or directory on the local filesystem.\\n\\nEither `--artifact-uri` or `--run-id` must be provided.\\n\\n    \\n    \\n    mlflow artifacts download [OPTIONS]\\n    \\n\\nOptions\\n\\n-r, \\\\--run-id <run_id>\\n    \\n\\nRun ID from which to download\\n\\n-a, \\\\--artifact-path <artifact_path>\\n    \\n\\nFor use with Run ID: if specified, a path relative to the run\\u00e2\\u0080\\u0099s root\\ndirectory to download\\n\\n-u, \\\\--artifact-uri <artifact_uri>\\n    \\n\\nURI pointing to the artifact file or artifacts directory; use as an\\nalternative to specifying \\u00e2\\u0080\\u0093run_id and \\u00e2\\u0080\\u0093artifact-path\\n\\n-d, \\\\--dst-path <dst_path>\\n    \\n\\nPath of the local filesystem destination directory to which to download the\\nspecified artifacts. If the directory does not exist, it is created. If\\nunspecified the artifacts are downloaded to a new uniquely-named directory on\\nthe local filesystem, unless the artifacts already exist on the local\\nfilesystem, in which case their local path is returned directly\\n\\n#### list\\n\\nReturn all the artifacts directly under run\\u00e2\\u0080\\u0099s root artifact directory, or a\\nsub-directory. The output is a JSON-formatted list.\\n\\n    \\n    \\n    mlflow artifacts list [OPTIONS]\\n    \\n\\nOptions\\n\\n-r, \\\\--run-id <run_id>\\n    \\n\\n**Required** Run ID to be listed\\n\\n-a, \\\\--artifact-path <artifact_path>\\n    \\n\\nIf specified, a path relative to the run\\u00e2\\u0080\\u0099s root directory to list.\\n\\n#### log-artifact\\n\\nLog a local file as an artifact of a run, optionally within a run-specific\\nartifact path.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 2637, \"end_char_idx\": 4781, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/cli.html","doc_id":"https://mlflow.org/docs/latest/cli.html","ref_doc_id":"https://mlflow.org/docs/latest/cli.html"}
{"_node_content":"{\"id_\": \"58afd1e8-9617-441f-9611-ca1cebb32fce\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/cli.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"2a8ac9b19cbddee491423c178812937f540f6dfd863499d20a2eb0461839b388\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"86317049-3e45-4f1f-894a-43939eb1cfce\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"bb463d140776457e21378c62acb12056d5c01134859a46fd309de7be354223c7\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"79b53024-8cf1-47f1-ac33-cc13ab157c00\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"229d33611e728741f1d1f8a47f2e93993dc1f2d2f2072dead7e4fff8a0de8b2a\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"If the directory does not exist, it is created. If\\nunspecified the artifacts are downloaded to a new uniquely-named directory on\\nthe local filesystem, unless the artifacts already exist on the local\\nfilesystem, in which case their local path is returned directly\\n\\n#### list\\n\\nReturn all the artifacts directly under run\\u00e2\\u0080\\u0099s root artifact directory, or a\\nsub-directory. The output is a JSON-formatted list.\\n\\n    \\n    \\n    mlflow artifacts list [OPTIONS]\\n    \\n\\nOptions\\n\\n-r, \\\\--run-id <run_id>\\n    \\n\\n**Required** Run ID to be listed\\n\\n-a, \\\\--artifact-path <artifact_path>\\n    \\n\\nIf specified, a path relative to the run\\u00e2\\u0080\\u0099s root directory to list.\\n\\n#### log-artifact\\n\\nLog a local file as an artifact of a run, optionally within a run-specific\\nartifact path. Run artifacts can be organized into directories, so you can\\nplace the artifact in a directory this way.\\n\\n    \\n    \\n    mlflow artifacts log-artifact [OPTIONS]\\n    \\n\\nOptions\\n\\n-l, \\\\--local-file <local_file>\\n    \\n\\n**Required** Local path to artifact to log\\n\\n-r, \\\\--run-id <run_id>\\n    \\n\\n**Required** Run ID into which we should log the artifact.\\n\\n-a, \\\\--artifact-path <artifact_path>\\n    \\n\\nIf specified, we will log the artifact into this subdirectory of the run\\u00e2\\u0080\\u0099s\\nartifact directory.\\n\\n#### log-artifacts\\n\\nLog the files within a local directory as an artifact of a run, optionally\\nwithin a run-specific artifact path. Run artifacts can be organized into\\ndirectories, so you can place the artifact in a directory this way.\\n\\n    \\n    \\n    mlflow artifacts log-artifacts [OPTIONS]\\n    \\n\\nOptions\\n\\n-l, \\\\--local-dir <local_dir>\\n    \\n\\n**Required** Directory of local artifacts to log\\n\\n-r, \\\\--run-id <run_id>\\n    \\n\\n**Required** Run ID into which we should log the artifact.\\n\\n-a, \\\\--artifact-path <artifact_path>\\n    \\n\\nIf specified, we will log the artifact into this subdirectory of the run\\u00e2\\u0080\\u0099s\\nartifact directory.\\n\\n### db\\n\\nCommands for managing an MLflow tracking database.\\n\\n    \\n    \\n    mlflow db [OPTIONS] COMMAND [ARGS]...\\n    \\n\\n#### upgrade\\n\\nUpgrade the schema of an MLflow tracking database to the latest supported\\nversion.\\n\\n**IMPORTANT** : Schema migrations can be slow and are not guaranteed to be\\ntransactional - **always take a backup of your database before running\\nmigrations**.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 4030, \"end_char_idx\": 6264, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/cli.html","doc_id":"https://mlflow.org/docs/latest/cli.html","ref_doc_id":"https://mlflow.org/docs/latest/cli.html"}
{"_node_content":"{\"id_\": \"79b53024-8cf1-47f1-ac33-cc13ab157c00\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/cli.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"2a8ac9b19cbddee491423c178812937f540f6dfd863499d20a2eb0461839b388\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"58afd1e8-9617-441f-9611-ca1cebb32fce\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"a551fff927cf103e8961bb4e3c0be1aef69bcbf90b1880b0622bef0d05325f02\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"0aeb3595-9b2a-45a6-a875-7cdc715f57b7\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"3b7e5213732b6b4adc3d72702713ba5ecd7ce7e93405d08ab0a7c7f8531f17f6\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"Run artifacts can be organized into\\ndirectories, so you can place the artifact in a directory this way.\\n\\n    \\n    \\n    mlflow artifacts log-artifacts [OPTIONS]\\n    \\n\\nOptions\\n\\n-l, \\\\--local-dir <local_dir>\\n    \\n\\n**Required** Directory of local artifacts to log\\n\\n-r, \\\\--run-id <run_id>\\n    \\n\\n**Required** Run ID into which we should log the artifact.\\n\\n-a, \\\\--artifact-path <artifact_path>\\n    \\n\\nIf specified, we will log the artifact into this subdirectory of the run\\u00e2\\u0080\\u0099s\\nartifact directory.\\n\\n### db\\n\\nCommands for managing an MLflow tracking database.\\n\\n    \\n    \\n    mlflow db [OPTIONS] COMMAND [ARGS]...\\n    \\n\\n#### upgrade\\n\\nUpgrade the schema of an MLflow tracking database to the latest supported\\nversion.\\n\\n**IMPORTANT** : Schema migrations can be slow and are not guaranteed to be\\ntransactional - **always take a backup of your database before running\\nmigrations**. The migrations README, which is located at\\n<https://github.com/mlflow/mlflow/blob/master/mlflow/store/db_migrations/README.md>,\\ndescribes large migrations and includes information about how to estimate\\ntheir performance and recover from failures.\\n\\n    \\n    \\n    mlflow db upgrade [OPTIONS] URL\\n    \\n\\nArguments\\n\\nURL\\n\\n    \\n\\nRequired argument\\n\\n### deployments\\n\\nDeploy MLflow models to custom targets. Run mlflow deployments help \\u00e2\\u0080\\u0093target-\\nname <target-name> for more details on the supported URI format and config\\noptions for a given target. Support is currently installed for deployment to:\\ndatabricks, http, https, openai, sagemaker\\n\\nSee all supported deployment targets and installation instructions in\\n<https://mlflow.org/docs/latest/plugins.html#community-plugins>\\n\\nYou can also write your own plugin for deployment to a custom target. For\\ninstructions on writing and distributing a plugin, see\\n<https://mlflow.org/docs/latest/plugins.html#writing-your-own-mlflow-plugins>.\\n\\n    \\n    \\n    mlflow deployments [OPTIONS] COMMAND [ARGS]...\\n    \\n\\n#### create\\n\\nDeploy the model at `model_uri` to the specified target.\\n\\nAdditional plugin-specific arguments may also be passed to this command, via\\n-C key=value\\n\\n    \\n    \\n    mlflow deployments create [OPTIONS]\\n    \\n\\nOptions\\n\\n\\\\--endpoint <endpoint>\\n\\n    \\n\\nName of the endpoint\\n\\n-C, \\\\--config <NAME=VALUE>\\n    \\n\\nExtra target-specific config for the model deployment, of the form -C\\nname=value.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 5399, \"end_char_idx\": 7703, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/cli.html","doc_id":"https://mlflow.org/docs/latest/cli.html","ref_doc_id":"https://mlflow.org/docs/latest/cli.html"}
{"_node_content":"{\"id_\": \"0aeb3595-9b2a-45a6-a875-7cdc715f57b7\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/cli.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"2a8ac9b19cbddee491423c178812937f540f6dfd863499d20a2eb0461839b388\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"79b53024-8cf1-47f1-ac33-cc13ab157c00\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"229d33611e728741f1d1f8a47f2e93993dc1f2d2f2072dead7e4fff8a0de8b2a\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"6ca66409-8b19-4d82-986f-bfdc0872ca21\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"cb5576947ae3edee0d44574ff16eb75508d7def42cdd11c5f7d89c052a1e10e6\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"Support is currently installed for deployment to:\\ndatabricks, http, https, openai, sagemaker\\n\\nSee all supported deployment targets and installation instructions in\\n<https://mlflow.org/docs/latest/plugins.html#community-plugins>\\n\\nYou can also write your own plugin for deployment to a custom target. For\\ninstructions on writing and distributing a plugin, see\\n<https://mlflow.org/docs/latest/plugins.html#writing-your-own-mlflow-plugins>.\\n\\n    \\n    \\n    mlflow deployments [OPTIONS] COMMAND [ARGS]...\\n    \\n\\n#### create\\n\\nDeploy the model at `model_uri` to the specified target.\\n\\nAdditional plugin-specific arguments may also be passed to this command, via\\n-C key=value\\n\\n    \\n    \\n    mlflow deployments create [OPTIONS]\\n    \\n\\nOptions\\n\\n\\\\--endpoint <endpoint>\\n\\n    \\n\\nName of the endpoint\\n\\n-C, \\\\--config <NAME=VALUE>\\n    \\n\\nExtra target-specific config for the model deployment, of the form -C\\nname=value. See documentation/help for your deployment target for a list of\\nsupported config options.\\n\\n\\\\--name <name>\\n\\n    \\n\\n**Required** Name of the deployment\\n\\n-t, \\\\--target <target>\\n    \\n\\n**Required** Deployment target URI. Run mlflow deployments help \\u00e2\\u0080\\u0093target-name\\n<target-name> for more details on the supported URI format and config options\\nfor a given target. Support is currently installed for deployment to:\\ndatabricks, http, https, openai, sagemaker\\n\\nSee all supported deployment targets and installation instructions at\\n<https://mlflow.org/docs/latest/plugins.html#community-plugins>\\n\\n-m, \\\\--model-uri <URI>\\n    \\n\\n**Required** URI to the model. A local path, a \\u00e2\\u0080\\u0098runs:/\\u00e2\\u0080\\u0099 URI, or a remote\\nstorage URI (e.g., an \\u00e2\\u0080\\u0098s3://\\u00e2\\u0080\\u0099 URI). For more information about supported\\nremote URIs for model artifacts, see\\nhttps://mlflow.org/docs/latest/tracking.html#artifact-stores\\n\\n-f, \\\\--flavor <flavor>\\n    \\n\\nWhich flavor to be deployed. This will be auto inferred if it\\u00e2\\u0080\\u0099s not given\\n\\n#### create-endpoint\\n\\nCreate an endpoint with the specified name at the specified target.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 6805, \"end_char_idx\": 8766, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/cli.html","doc_id":"https://mlflow.org/docs/latest/cli.html","ref_doc_id":"https://mlflow.org/docs/latest/cli.html"}
{"_node_content":"{\"id_\": \"6ca66409-8b19-4d82-986f-bfdc0872ca21\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/cli.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"2a8ac9b19cbddee491423c178812937f540f6dfd863499d20a2eb0461839b388\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"0aeb3595-9b2a-45a6-a875-7cdc715f57b7\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"3b7e5213732b6b4adc3d72702713ba5ecd7ce7e93405d08ab0a7c7f8531f17f6\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"53447b4f-2e88-4c7b-a5f8-7c479ea482ae\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"4773927ba5dbe78e18fb5837d92fe4ef20a2b4055a775874246ceb46dd93e976\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"Support is currently installed for deployment to:\\ndatabricks, http, https, openai, sagemaker\\n\\nSee all supported deployment targets and installation instructions at\\n<https://mlflow.org/docs/latest/plugins.html#community-plugins>\\n\\n-m, \\\\--model-uri <URI>\\n    \\n\\n**Required** URI to the model. A local path, a \\u00e2\\u0080\\u0098runs:/\\u00e2\\u0080\\u0099 URI, or a remote\\nstorage URI (e.g., an \\u00e2\\u0080\\u0098s3://\\u00e2\\u0080\\u0099 URI). For more information about supported\\nremote URIs for model artifacts, see\\nhttps://mlflow.org/docs/latest/tracking.html#artifact-stores\\n\\n-f, \\\\--flavor <flavor>\\n    \\n\\nWhich flavor to be deployed. This will be auto inferred if it\\u00e2\\u0080\\u0099s not given\\n\\n#### create-endpoint\\n\\nCreate an endpoint with the specified name at the specified target.\\n\\nAdditional plugin-specific arguments may also be passed to this command, via\\n-C key=value\\n\\n    \\n    \\n    mlflow deployments create-endpoint [OPTIONS]\\n    \\n\\nOptions\\n\\n-C, \\\\--config <NAME=VALUE>\\n    \\n\\nExtra target-specific config for the endpoint, of the form -C name=value. See\\ndocumentation/help for your deployment target for a list of supported config\\noptions.\\n\\n\\\\--endpoint <endpoint>\\n\\n    \\n\\n**Required** Name of the endpoint\\n\\n-t, \\\\--target <target>\\n    \\n\\n**Required** Deployment target URI. Run mlflow deployments help \\u00e2\\u0080\\u0093target-name\\n<target-name> for more details on the supported URI format and config options\\nfor a given target. Support is currently installed for deployment to:\\ndatabricks, http, https, openai, sagemaker\\n\\nSee all supported deployment targets and installation instructions at\\n<https://mlflow.org/docs/latest/plugins.html#community-plugins>\\n\\n#### delete\\n\\nDelete the deployment with name given at \\u00e2\\u0080\\u0093name from the specified target.\\n\\n    \\n    \\n    mlflow deployments delete [OPTIONS]\\n    \\n\\nOptions\\n\\n\\\\--endpoint <endpoint>\\n\\n    \\n\\nName of the endpoint\\n\\n-C, \\\\--config <NAME=VALUE>\\n    \\n\\nExtra target-specific config for the model deployment, of the form -C\\nname=value. See documentation/help for your deployment target for a list of\\nsupported config options.\\n\\n\\\\--name <name>\\n\\n    \\n\\n**Required** Name of the deployment\\n\\n-t, \\\\--target <target>\\n    \\n\\n**Required** Deployment target URI.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 8060, \"end_char_idx\": 10167, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/cli.html","doc_id":"https://mlflow.org/docs/latest/cli.html","ref_doc_id":"https://mlflow.org/docs/latest/cli.html"}
{"_node_content":"{\"id_\": \"53447b4f-2e88-4c7b-a5f8-7c479ea482ae\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/cli.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"2a8ac9b19cbddee491423c178812937f540f6dfd863499d20a2eb0461839b388\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"6ca66409-8b19-4d82-986f-bfdc0872ca21\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"cb5576947ae3edee0d44574ff16eb75508d7def42cdd11c5f7d89c052a1e10e6\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"8e46571c-5609-4503-8f79-368109cd60f8\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"8c2616656b25f7fe47e5a0dc84e1d7db1657bcb701faedf1cc42ebd3d772bca1\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"Support is currently installed for deployment to:\\ndatabricks, http, https, openai, sagemaker\\n\\nSee all supported deployment targets and installation instructions at\\n<https://mlflow.org/docs/latest/plugins.html#community-plugins>\\n\\n#### delete\\n\\nDelete the deployment with name given at \\u00e2\\u0080\\u0093name from the specified target.\\n\\n    \\n    \\n    mlflow deployments delete [OPTIONS]\\n    \\n\\nOptions\\n\\n\\\\--endpoint <endpoint>\\n\\n    \\n\\nName of the endpoint\\n\\n-C, \\\\--config <NAME=VALUE>\\n    \\n\\nExtra target-specific config for the model deployment, of the form -C\\nname=value. See documentation/help for your deployment target for a list of\\nsupported config options.\\n\\n\\\\--name <name>\\n\\n    \\n\\n**Required** Name of the deployment\\n\\n-t, \\\\--target <target>\\n    \\n\\n**Required** Deployment target URI. Run mlflow deployments help \\u00e2\\u0080\\u0093target-name\\n<target-name> for more details on the supported URI format and config options\\nfor a given target. Support is currently installed for deployment to:\\ndatabricks, http, https, openai, sagemaker\\n\\nSee all supported deployment targets and installation instructions at\\n<https://mlflow.org/docs/latest/plugins.html#community-plugins>\\n\\n#### delete-endpoint\\n\\nDelete the specified endpoint at the specified target\\n\\n    \\n    \\n    mlflow deployments delete-endpoint [OPTIONS]\\n    \\n\\nOptions\\n\\n\\\\--endpoint <endpoint>\\n\\n    \\n\\n**Required** Name of the endpoint\\n\\n-t, \\\\--target <target>\\n    \\n\\n**Required** Deployment target URI. Run mlflow deployments help \\u00e2\\u0080\\u0093target-name\\n<target-name> for more details on the supported URI format and config options\\nfor a given target. Support is currently installed for deployment to:\\ndatabricks, http, https, openai, sagemaker\\n\\nSee all supported deployment targets and installation instructions at\\n<https://mlflow.org/docs/latest/plugins.html#community-plugins>\\n\\n#### explain\\n\\nGenerate explanations of model predictions on the specified input for the\\ndeployed model for the given input(s). Explanation output formats vary by\\ndeployment target, and can include details like feature importance for\\nunderstanding/debugging predictions. Run mlflow deployments help or consult\\nthe documentation for your plugin for details on explanation format.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 9402, \"end_char_idx\": 11566, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/cli.html","doc_id":"https://mlflow.org/docs/latest/cli.html","ref_doc_id":"https://mlflow.org/docs/latest/cli.html"}
{"_node_content":"{\"id_\": \"8e46571c-5609-4503-8f79-368109cd60f8\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/cli.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"2a8ac9b19cbddee491423c178812937f540f6dfd863499d20a2eb0461839b388\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"53447b4f-2e88-4c7b-a5f8-7c479ea482ae\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"4773927ba5dbe78e18fb5837d92fe4ef20a2b4055a775874246ceb46dd93e976\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"cc79a43c-fc2c-4bbb-83b4-3eddd3b3a645\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"3b6712798c55c626be9678f8cdab25391d873137ac351bb38fa1ed50667a1f84\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"Run mlflow deployments help \\u00e2\\u0080\\u0093target-name\\n<target-name> for more details on the supported URI format and config options\\nfor a given target. Support is currently installed for deployment to:\\ndatabricks, http, https, openai, sagemaker\\n\\nSee all supported deployment targets and installation instructions at\\n<https://mlflow.org/docs/latest/plugins.html#community-plugins>\\n\\n#### explain\\n\\nGenerate explanations of model predictions on the specified input for the\\ndeployed model for the given input(s). Explanation output formats vary by\\ndeployment target, and can include details like feature importance for\\nunderstanding/debugging predictions. Run mlflow deployments help or consult\\nthe documentation for your plugin for details on explanation format. For\\ninformation about the input data formats accepted by this function, see the\\nfollowing documentation:\\n<https://www.mlflow.org/docs/latest/models.html#built-in-deployment-tools>\\n\\n    \\n    \\n    mlflow deployments explain [OPTIONS]\\n    \\n\\nOptions\\n\\n\\\\--name <name>\\n\\n    \\n\\nName of the deployment. Exactly one of \\u00e2\\u0080\\u0093name or \\u00e2\\u0080\\u0093endpoint must be\\nspecified.\\n\\n\\\\--endpoint <endpoint>\\n\\n    \\n\\nName of the endpoint. Exactly one of \\u00e2\\u0080\\u0093name or \\u00e2\\u0080\\u0093endpoint must be specified.\\n\\n-t, \\\\--target <target>\\n    \\n\\n**Required** Deployment target URI. Run mlflow deployments help \\u00e2\\u0080\\u0093target-name\\n<target-name> for more details on the supported URI format and config options\\nfor a given target. Support is currently installed for deployment to:\\ndatabricks, http, https, openai, sagemaker\\n\\nSee all supported deployment targets and installation instructions at\\n<https://mlflow.org/docs/latest/plugins.html#community-plugins>\\n\\n-I, \\\\--input-path <input_path>\\n    \\n\\n**Required** Path to input prediction payload file. The file canbe a JSON\\n(Python Dict) or CSV (pandas DataFrame). If the file is a CSV, the user must\\nspecifythe \\u00e2\\u0080\\u0093content-type csv option.\\n\\n-O, \\\\--output-path <output_path>\\n    \\n\\nFile to output results to as a JSON file. If not provided, prints output to\\nstdout.\\n\\n#### get\\n\\nPrint a detailed description of the deployment with name given at `--name` in\\nthe specified target.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 10819, \"end_char_idx\": 12924, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/cli.html","doc_id":"https://mlflow.org/docs/latest/cli.html","ref_doc_id":"https://mlflow.org/docs/latest/cli.html"}
{"_node_content":"{\"id_\": \"cc79a43c-fc2c-4bbb-83b4-3eddd3b3a645\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/cli.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"2a8ac9b19cbddee491423c178812937f540f6dfd863499d20a2eb0461839b388\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"8e46571c-5609-4503-8f79-368109cd60f8\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"8c2616656b25f7fe47e5a0dc84e1d7db1657bcb701faedf1cc42ebd3d772bca1\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"8e2fbd12-ad2c-4dd1-9766-de96a28bf4a0\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"fa8ae7a18a1706b11a58d6fb551dff966b71edf429a2663aa859204ed6507361\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"Support is currently installed for deployment to:\\ndatabricks, http, https, openai, sagemaker\\n\\nSee all supported deployment targets and installation instructions at\\n<https://mlflow.org/docs/latest/plugins.html#community-plugins>\\n\\n-I, \\\\--input-path <input_path>\\n    \\n\\n**Required** Path to input prediction payload file. The file canbe a JSON\\n(Python Dict) or CSV (pandas DataFrame). If the file is a CSV, the user must\\nspecifythe \\u00e2\\u0080\\u0093content-type csv option.\\n\\n-O, \\\\--output-path <output_path>\\n    \\n\\nFile to output results to as a JSON file. If not provided, prints output to\\nstdout.\\n\\n#### get\\n\\nPrint a detailed description of the deployment with name given at `--name` in\\nthe specified target.\\n\\n    \\n    \\n    mlflow deployments get [OPTIONS]\\n    \\n\\nOptions\\n\\n\\\\--endpoint <endpoint>\\n\\n    \\n\\nName of the endpoint\\n\\n\\\\--name <name>\\n\\n    \\n\\n**Required** Name of the deployment\\n\\n-t, \\\\--target <target>\\n    \\n\\n**Required** Deployment target URI. Run mlflow deployments help \\u00e2\\u0080\\u0093target-name\\n<target-name> for more details on the supported URI format and config options\\nfor a given target. Support is currently installed for deployment to:\\ndatabricks, http, https, openai, sagemaker\\n\\nSee all supported deployment targets and installation instructions at\\n<https://mlflow.org/docs/latest/plugins.html#community-plugins>\\n\\n#### get-endpoint\\n\\nGet details for the specified endpoint at the specified target\\n\\n    \\n    \\n    mlflow deployments get-endpoint [OPTIONS]\\n    \\n\\nOptions\\n\\n\\\\--endpoint <endpoint>\\n\\n    \\n\\n**Required** Name of the endpoint\\n\\n-t, \\\\--target <target>\\n    \\n\\n**Required** Deployment target URI. Run mlflow deployments help \\u00e2\\u0080\\u0093target-name\\n<target-name> for more details on the supported URI format and config options\\nfor a given target. Support is currently installed for deployment to:\\ndatabricks, http, https, openai, sagemaker\\n\\nSee all supported deployment targets and installation instructions at\\n<https://mlflow.org/docs/latest/plugins.html#community-plugins>\\n\\n#### help\\n\\nDisplay additional help for a specific deployment target, e.g. info on target-\\nspecific config options and the target\\u00e2\\u0080\\u0099s URI format.\\n\\n    \\n    \\n    mlflow deployments help [OPTIONS]\\n    \\n\\nOptions\\n\\n-t, \\\\--target <target>\\n    \\n\\n**Required** Deployment target URI.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 12234, \"end_char_idx\": 14461, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/cli.html","doc_id":"https://mlflow.org/docs/latest/cli.html","ref_doc_id":"https://mlflow.org/docs/latest/cli.html"}
{"_node_content":"{\"id_\": \"8e2fbd12-ad2c-4dd1-9766-de96a28bf4a0\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/cli.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"2a8ac9b19cbddee491423c178812937f540f6dfd863499d20a2eb0461839b388\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"cc79a43c-fc2c-4bbb-83b4-3eddd3b3a645\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"3b6712798c55c626be9678f8cdab25391d873137ac351bb38fa1ed50667a1f84\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"1ce838aa-d5b0-43f4-bb8b-408294f36f83\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"c557253c2ebf887b5c07aac89b65b5784a216fa6fdd8d19a4d913058f843b49d\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"Run mlflow deployments help \\u00e2\\u0080\\u0093target-name\\n<target-name> for more details on the supported URI format and config options\\nfor a given target. Support is currently installed for deployment to:\\ndatabricks, http, https, openai, sagemaker\\n\\nSee all supported deployment targets and installation instructions at\\n<https://mlflow.org/docs/latest/plugins.html#community-plugins>\\n\\n#### help\\n\\nDisplay additional help for a specific deployment target, e.g. info on target-\\nspecific config options and the target\\u00e2\\u0080\\u0099s URI format.\\n\\n    \\n    \\n    mlflow deployments help [OPTIONS]\\n    \\n\\nOptions\\n\\n-t, \\\\--target <target>\\n    \\n\\n**Required** Deployment target URI. Run mlflow deployments help \\u00e2\\u0080\\u0093target-name\\n<target-name> for more details on the supported URI format and config options\\nfor a given target. Support is currently installed for deployment to:\\ndatabricks, http, https, openai, sagemaker\\n\\nSee all supported deployment targets and installation instructions at\\n<https://mlflow.org/docs/latest/plugins.html#community-plugins>\\n\\n#### list\\n\\nList the names of all model deployments in the specified target. These names\\ncan be used with the delete, update, and get commands.\\n\\n    \\n    \\n    mlflow deployments list [OPTIONS]\\n    \\n\\nOptions\\n\\n\\\\--endpoint <endpoint>\\n\\n    \\n\\nName of the endpoint\\n\\n-t, \\\\--target <target>\\n    \\n\\n**Required** Deployment target URI. Run mlflow deployments help \\u00e2\\u0080\\u0093target-name\\n<target-name> for more details on the supported URI format and config options\\nfor a given target. Support is currently installed for deployment to:\\ndatabricks, http, https, openai, sagemaker\\n\\nSee all supported deployment targets and installation instructions at\\n<https://mlflow.org/docs/latest/plugins.html#community-plugins>\\n\\n#### list-endpoints\\n\\nList all endpoints at the specified target\\n\\n    \\n    \\n    mlflow deployments list-endpoints [OPTIONS]\\n    \\n\\nOptions\\n\\n-t, \\\\--target <target>\\n    \\n\\n**Required** Deployment target URI. Run mlflow deployments help \\u00e2\\u0080\\u0093target-name\\n<target-name> for more details on the supported URI format and config options\\nfor a given target.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 13818, \"end_char_idx\": 15869, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/cli.html","doc_id":"https://mlflow.org/docs/latest/cli.html","ref_doc_id":"https://mlflow.org/docs/latest/cli.html"}
{"_node_content":"{\"id_\": \"1ce838aa-d5b0-43f4-bb8b-408294f36f83\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/cli.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"2a8ac9b19cbddee491423c178812937f540f6dfd863499d20a2eb0461839b388\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"8e2fbd12-ad2c-4dd1-9766-de96a28bf4a0\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"fa8ae7a18a1706b11a58d6fb551dff966b71edf429a2663aa859204ed6507361\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"9c424bc0-1a33-4b0a-a5f7-7d9c656f0dd5\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"71c9a8ca807369affdda0e558923316c678a19009bf86539a169735eb3d3f8af\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"mlflow deployments list [OPTIONS]\\n    \\n\\nOptions\\n\\n\\\\--endpoint <endpoint>\\n\\n    \\n\\nName of the endpoint\\n\\n-t, \\\\--target <target>\\n    \\n\\n**Required** Deployment target URI. Run mlflow deployments help \\u00e2\\u0080\\u0093target-name\\n<target-name> for more details on the supported URI format and config options\\nfor a given target. Support is currently installed for deployment to:\\ndatabricks, http, https, openai, sagemaker\\n\\nSee all supported deployment targets and installation instructions at\\n<https://mlflow.org/docs/latest/plugins.html#community-plugins>\\n\\n#### list-endpoints\\n\\nList all endpoints at the specified target\\n\\n    \\n    \\n    mlflow deployments list-endpoints [OPTIONS]\\n    \\n\\nOptions\\n\\n-t, \\\\--target <target>\\n    \\n\\n**Required** Deployment target URI. Run mlflow deployments help \\u00e2\\u0080\\u0093target-name\\n<target-name> for more details on the supported URI format and config options\\nfor a given target. Support is currently installed for deployment to:\\ndatabricks, http, https, openai, sagemaker\\n\\nSee all supported deployment targets and installation instructions at\\n<https://mlflow.org/docs/latest/plugins.html#community-plugins>\\n\\n#### predict\\n\\nPredict the results for the deployed model for the given input(s)\\n\\n    \\n    \\n    mlflow deployments predict [OPTIONS]\\n    \\n\\nOptions\\n\\n\\\\--name <name>\\n\\n    \\n\\nName of the deployment. Exactly one of \\u00e2\\u0080\\u0093name or \\u00e2\\u0080\\u0093endpoint must be\\nspecified.\\n\\n\\\\--endpoint <endpoint>\\n\\n    \\n\\nName of the endpoint. Exactly one of \\u00e2\\u0080\\u0093name or \\u00e2\\u0080\\u0093endpoint must be specified.\\n\\n-t, \\\\--target <target>\\n    \\n\\n**Required** Deployment target URI. Run mlflow deployments help \\u00e2\\u0080\\u0093target-name\\n<target-name> for more details on the supported URI format and config options\\nfor a given target. Support is currently installed for deployment to:\\ndatabricks, http, https, openai, sagemaker\\n\\nSee all supported deployment targets and installation instructions at\\n<https://mlflow.org/docs/latest/plugins.html#community-plugins>\\n\\n-I, \\\\--input-path <input_path>\\n    \\n\\n**Required** Path to input prediction payload file. The file canbe a JSON\\n(Python Dict) or CSV (pandas DataFrame). If the file is a CSV, the user must\\nspecifythe \\u00e2\\u0080\\u0093content-type csv option.\\n\\n-O, \\\\--output-path <output_path>\\n    \\n\\nFile to output results to as a JSON file.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 14990, \"end_char_idx\": 17203, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/cli.html","doc_id":"https://mlflow.org/docs/latest/cli.html","ref_doc_id":"https://mlflow.org/docs/latest/cli.html"}
{"_node_content":"{\"id_\": \"9c424bc0-1a33-4b0a-a5f7-7d9c656f0dd5\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/cli.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"2a8ac9b19cbddee491423c178812937f540f6dfd863499d20a2eb0461839b388\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"1ce838aa-d5b0-43f4-bb8b-408294f36f83\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"c557253c2ebf887b5c07aac89b65b5784a216fa6fdd8d19a4d913058f843b49d\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"500c631d-b40b-4c12-9b7c-bc81fe8fa875\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"dff729cebca10cd01479ddbb15f05c172153c27ef105ba571d0774b5cd2f209e\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"Exactly one of \\u00e2\\u0080\\u0093name or \\u00e2\\u0080\\u0093endpoint must be specified.\\n\\n-t, \\\\--target <target>\\n    \\n\\n**Required** Deployment target URI. Run mlflow deployments help \\u00e2\\u0080\\u0093target-name\\n<target-name> for more details on the supported URI format and config options\\nfor a given target. Support is currently installed for deployment to:\\ndatabricks, http, https, openai, sagemaker\\n\\nSee all supported deployment targets and installation instructions at\\n<https://mlflow.org/docs/latest/plugins.html#community-plugins>\\n\\n-I, \\\\--input-path <input_path>\\n    \\n\\n**Required** Path to input prediction payload file. The file canbe a JSON\\n(Python Dict) or CSV (pandas DataFrame). If the file is a CSV, the user must\\nspecifythe \\u00e2\\u0080\\u0093content-type csv option.\\n\\n-O, \\\\--output-path <output_path>\\n    \\n\\nFile to output results to as a JSON file. If not provided, prints output to\\nstdout.\\n\\n#### run-local\\n\\nDeploy the model locally. This has very similar signature to `create` API\\n\\n    \\n    \\n    mlflow deployments run-local [OPTIONS]\\n    \\n\\nOptions\\n\\n-C, \\\\--config <NAME=VALUE>\\n    \\n\\nExtra target-specific config for the model deployment, of the form -C\\nname=value. See documentation/help for your deployment target for a list of\\nsupported config options.\\n\\n\\\\--name <name>\\n\\n    \\n\\n**Required** Name of the deployment\\n\\n-t, \\\\--target <target>\\n    \\n\\n**Required** Deployment target URI. Run mlflow deployments help \\u00e2\\u0080\\u0093target-name\\n<target-name> for more details on the supported URI format and config options\\nfor a given target. Support is currently installed for deployment to:\\ndatabricks, http, https, openai, sagemaker\\n\\nSee all supported deployment targets and installation instructions at\\n<https://mlflow.org/docs/latest/plugins.html#community-plugins>\\n\\n-m, \\\\--model-uri <URI>\\n    \\n\\n**Required** URI to the model. A local path, a \\u00e2\\u0080\\u0098runs:/\\u00e2\\u0080\\u0099 URI, or a remote\\nstorage URI (e.g., an \\u00e2\\u0080\\u0098s3://\\u00e2\\u0080\\u0099 URI). For more information about supported\\nremote URIs for model artifacts, see\\nhttps://mlflow.org/docs/latest/tracking.html#artifact-stores\\n\\n-f, \\\\--flavor <flavor>\\n    \\n\\nWhich flavor to be deployed.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 16402, \"end_char_idx\": 18445, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/cli.html","doc_id":"https://mlflow.org/docs/latest/cli.html","ref_doc_id":"https://mlflow.org/docs/latest/cli.html"}
{"_node_content":"{\"id_\": \"500c631d-b40b-4c12-9b7c-bc81fe8fa875\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/cli.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"2a8ac9b19cbddee491423c178812937f540f6dfd863499d20a2eb0461839b388\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"9c424bc0-1a33-4b0a-a5f7-7d9c656f0dd5\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"71c9a8ca807369affdda0e558923316c678a19009bf86539a169735eb3d3f8af\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"fc1ecd83-737d-4325-86df-e7d54f96afb4\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"b2106a74a54900aeeb44a3d3e19577194a212ed88c19aa6bf6982a2af1bd9d15\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"Run mlflow deployments help \\u00e2\\u0080\\u0093target-name\\n<target-name> for more details on the supported URI format and config options\\nfor a given target. Support is currently installed for deployment to:\\ndatabricks, http, https, openai, sagemaker\\n\\nSee all supported deployment targets and installation instructions at\\n<https://mlflow.org/docs/latest/plugins.html#community-plugins>\\n\\n-m, \\\\--model-uri <URI>\\n    \\n\\n**Required** URI to the model. A local path, a \\u00e2\\u0080\\u0098runs:/\\u00e2\\u0080\\u0099 URI, or a remote\\nstorage URI (e.g., an \\u00e2\\u0080\\u0098s3://\\u00e2\\u0080\\u0099 URI). For more information about supported\\nremote URIs for model artifacts, see\\nhttps://mlflow.org/docs/latest/tracking.html#artifact-stores\\n\\n-f, \\\\--flavor <flavor>\\n    \\n\\nWhich flavor to be deployed. This will be auto inferred if it\\u00e2\\u0080\\u0099s not given\\n\\n#### update\\n\\nUpdate the deployment with ID deployment_id in the specified target. You can\\nupdate the URI of the model and/or the flavor of the deployed model (in which\\ncase the model URI must also be specified).\\n\\nAdditional plugin-specific arguments may also be passed to this command, via\\n-C key=value.\\n\\n    \\n    \\n    mlflow deployments update [OPTIONS]\\n    \\n\\nOptions\\n\\n\\\\--endpoint <endpoint>\\n\\n    \\n\\nName of the endpoint\\n\\n-C, \\\\--config <NAME=VALUE>\\n    \\n\\nExtra target-specific config for the model deployment, of the form -C\\nname=value. See documentation/help for your deployment target for a list of\\nsupported config options.\\n\\n\\\\--name <name>\\n\\n    \\n\\n**Required** Name of the deployment\\n\\n-t, \\\\--target <target>\\n    \\n\\n**Required** Deployment target URI. Run mlflow deployments help \\u00e2\\u0080\\u0093target-name\\n<target-name> for more details on the supported URI format and config options\\nfor a given target. Support is currently installed for deployment to:\\ndatabricks, http, https, openai, sagemaker\\n\\nSee all supported deployment targets and installation instructions at\\n<https://mlflow.org/docs/latest/plugins.html#community-plugins>\\n\\n-m, \\\\--model-uri <URI>\\n    \\n\\nURI to the model. A local path, a \\u00e2\\u0080\\u0098runs:/\\u00e2\\u0080\\u0099 URI, or a remote storage URI\\n(e.g., an \\u00e2\\u0080\\u0098s3://\\u00e2\\u0080\\u0099 URI).\", \"mimetype\": \"text/plain\", \"start_char_idx\": 17736, \"end_char_idx\": 19748, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/cli.html","doc_id":"https://mlflow.org/docs/latest/cli.html","ref_doc_id":"https://mlflow.org/docs/latest/cli.html"}
{"_node_content":"{\"id_\": \"fc1ecd83-737d-4325-86df-e7d54f96afb4\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/cli.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"2a8ac9b19cbddee491423c178812937f540f6dfd863499d20a2eb0461839b388\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"500c631d-b40b-4c12-9b7c-bc81fe8fa875\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"dff729cebca10cd01479ddbb15f05c172153c27ef105ba571d0774b5cd2f209e\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"8e910682-5d31-41f1-966f-8b31bf95414a\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"97286b43c1bef533b6debad582ccde6ab5b758a8a40ed0f88fbf6299f73d78b1\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"See documentation/help for your deployment target for a list of\\nsupported config options.\\n\\n\\\\--name <name>\\n\\n    \\n\\n**Required** Name of the deployment\\n\\n-t, \\\\--target <target>\\n    \\n\\n**Required** Deployment target URI. Run mlflow deployments help \\u00e2\\u0080\\u0093target-name\\n<target-name> for more details on the supported URI format and config options\\nfor a given target. Support is currently installed for deployment to:\\ndatabricks, http, https, openai, sagemaker\\n\\nSee all supported deployment targets and installation instructions at\\n<https://mlflow.org/docs/latest/plugins.html#community-plugins>\\n\\n-m, \\\\--model-uri <URI>\\n    \\n\\nURI to the model. A local path, a \\u00e2\\u0080\\u0098runs:/\\u00e2\\u0080\\u0099 URI, or a remote storage URI\\n(e.g., an \\u00e2\\u0080\\u0098s3://\\u00e2\\u0080\\u0099 URI). For more information about supported remote URIs\\nfor model artifacts, see\\nhttps://mlflow.org/docs/latest/tracking.html#artifact-stores\\n\\n-f, \\\\--flavor <flavor>\\n    \\n\\nWhich flavor to be deployed. This will be auto inferred if it\\u00e2\\u0080\\u0099s not given\\n\\n#### update-endpoint\\n\\nUpdate the specified endpoint at the specified target.\\n\\nAdditional plugin-specific arguments may also be passed to this command, via\\n-C key=value\\n\\n    \\n    \\n    mlflow deployments update-endpoint [OPTIONS]\\n    \\n\\nOptions\\n\\n-C, \\\\--config <NAME=VALUE>\\n    \\n\\nExtra target-specific config for the endpoint, of the form -C name=value. See\\ndocumentation/help for your deployment target for a list of supported config\\noptions.\\n\\n\\\\--endpoint <endpoint>\\n\\n    \\n\\n**Required** Name of the endpoint\\n\\n-t, \\\\--target <target>\\n    \\n\\n**Required** Deployment target URI. Run mlflow deployments help \\u00e2\\u0080\\u0093target-name\\n<target-name> for more details on the supported URI format and config options\\nfor a given target. Support is currently installed for deployment to:\\ndatabricks, http, https, openai, sagemaker\\n\\nSee all supported deployment targets and installation instructions at\\n<https://mlflow.org/docs/latest/plugins.html#community-plugins>\\n\\n### doctor\\n\\nPrints out useful information for debugging issues with MLflow.\\n\\n    \\n    \\n    mlflow doctor [OPTIONS]\\n    \\n\\nOptions\\n\\n\\\\--mask-envs\\n\\n    \\n\\nIf set (the default behavior without setting this flag is not to obfuscate\\ninformation), mask the MLflow environment variable values (e.g.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 19031, \"end_char_idx\": 21220, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/cli.html","doc_id":"https://mlflow.org/docs/latest/cli.html","ref_doc_id":"https://mlflow.org/docs/latest/cli.html"}
{"_node_content":"{\"id_\": \"8e910682-5d31-41f1-966f-8b31bf95414a\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/cli.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"2a8ac9b19cbddee491423c178812937f540f6dfd863499d20a2eb0461839b388\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"fc1ecd83-737d-4325-86df-e7d54f96afb4\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"b2106a74a54900aeeb44a3d3e19577194a212ed88c19aa6bf6982a2af1bd9d15\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"f84d5e14-5642-4a48-b954-36e6ae6cb7f3\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"cf88fb061b6a43f0b3119c6683c48b8f3e198541fe9cbe073706873cf05384ec\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"See\\ndocumentation/help for your deployment target for a list of supported config\\noptions.\\n\\n\\\\--endpoint <endpoint>\\n\\n    \\n\\n**Required** Name of the endpoint\\n\\n-t, \\\\--target <target>\\n    \\n\\n**Required** Deployment target URI. Run mlflow deployments help \\u00e2\\u0080\\u0093target-name\\n<target-name> for more details on the supported URI format and config options\\nfor a given target. Support is currently installed for deployment to:\\ndatabricks, http, https, openai, sagemaker\\n\\nSee all supported deployment targets and installation instructions at\\n<https://mlflow.org/docs/latest/plugins.html#community-plugins>\\n\\n### doctor\\n\\nPrints out useful information for debugging issues with MLflow.\\n\\n    \\n    \\n    mlflow doctor [OPTIONS]\\n    \\n\\nOptions\\n\\n\\\\--mask-envs\\n\\n    \\n\\nIf set (the default behavior without setting this flag is not to obfuscate\\ninformation), mask the MLflow environment variable values (e.g.\\n\\u00e2\\u0080\\u009cMLFLOW_ENV_VAR\\u00e2\\u0080\\u009d: \\u00e2\\u0080\\u009c***\\u00e2\\u0080\\u009d) in the output to prevent leaking sensitive\\ninformation.\\n\\n### experiments\\n\\nManage experiments. To manage experiments associated with a tracking server,\\nset the MLFLOW_TRACKING_URI environment variable to the URL of the desired\\nserver.\\n\\n    \\n    \\n    mlflow experiments [OPTIONS] COMMAND [ARGS]...\\n    \\n\\n#### create\\n\\nCreate an experiment.\\n\\nAll artifacts generated by runs related to this experiment will be stored\\nunder artifact location, organized under specific run_id sub-directories.\\n\\nImplementation of experiment and metadata store is dependent on backend\\nstorage. `FileStore` creates a folder for each experiment ID and stores\\nmetadata in `meta.yaml`. Runs are stored as subfolders.\\n\\n    \\n    \\n    mlflow experiments create [OPTIONS]\\n    \\n\\nOptions\\n\\n-n, \\\\--experiment-name <experiment_name>\\n    \\n\\n**Required**\\n\\n-l, \\\\--artifact-location <artifact_location>\\n    \\n\\nBase location for runs to store artifact results. Artifacts will be stored at\\n$artifact_location/$run_id/artifacts. See\\n<https://mlflow.org/docs/latest/tracking.html#where-runs-are-recorded> for\\nmore info on the properties of artifact location. If no location is provided,\\nthe tracking server will pick a default.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 20341, \"end_char_idx\": 22432, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/cli.html","doc_id":"https://mlflow.org/docs/latest/cli.html","ref_doc_id":"https://mlflow.org/docs/latest/cli.html"}
{"_node_content":"{\"id_\": \"f84d5e14-5642-4a48-b954-36e6ae6cb7f3\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/cli.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"2a8ac9b19cbddee491423c178812937f540f6dfd863499d20a2eb0461839b388\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"8e910682-5d31-41f1-966f-8b31bf95414a\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"97286b43c1bef533b6debad582ccde6ab5b758a8a40ed0f88fbf6299f73d78b1\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"5ee252a6-077f-4c4d-9e93-c371086578d7\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"f5e1acc1ccc61d2120d261972a740c141e79c5508f31171f76aab46a24aba0c3\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"mlflow experiments [OPTIONS] COMMAND [ARGS]...\\n    \\n\\n#### create\\n\\nCreate an experiment.\\n\\nAll artifacts generated by runs related to this experiment will be stored\\nunder artifact location, organized under specific run_id sub-directories.\\n\\nImplementation of experiment and metadata store is dependent on backend\\nstorage. `FileStore` creates a folder for each experiment ID and stores\\nmetadata in `meta.yaml`. Runs are stored as subfolders.\\n\\n    \\n    \\n    mlflow experiments create [OPTIONS]\\n    \\n\\nOptions\\n\\n-n, \\\\--experiment-name <experiment_name>\\n    \\n\\n**Required**\\n\\n-l, \\\\--artifact-location <artifact_location>\\n    \\n\\nBase location for runs to store artifact results. Artifacts will be stored at\\n$artifact_location/$run_id/artifacts. See\\n<https://mlflow.org/docs/latest/tracking.html#where-runs-are-recorded> for\\nmore info on the properties of artifact location. If no location is provided,\\nthe tracking server will pick a default.\\n\\n#### csv\\n\\nGenerate CSV with all runs for an experiment\\n\\n    \\n    \\n    mlflow experiments csv [OPTIONS]\\n    \\n\\nOptions\\n\\n-x, \\\\--experiment-id <experiment_id>\\n    \\n\\n**Required**\\n\\n-o, \\\\--filename <filename>\\n    \\n\\n#### delete\\n\\nMark an active experiment for deletion. This also applies to experiment\\u00e2\\u0080\\u0099s\\nmetadata, runs and associated data, and artifacts if they are store in default\\nlocation. Use `list` command to view artifact location. Command will throw an\\nerror if experiment is not found or already marked for deletion.\\n\\nExperiments marked for deletion can be restored using `restore` command,\\nunless they are permanently deleted.\\n\\nSpecific implementation of deletion is dependent on backend stores.\\n`FileStore` moves experiments marked for deletion under a `.trash` folder\\nunder the main folder used to instantiate `FileStore`. Experiments marked for\\ndeletion can be permanently deleted by clearing the `.trash` folder. It is\\nrecommended to use a `cron` job or an alternate workflow mechanism to clear\\n`.trash` folder.\\n\\n    \\n    \\n    mlflow experiments delete [OPTIONS]\\n    \\n\\nOptions\\n\\n-x, \\\\--experiment-id <experiment_id>\\n    \\n\\n**Required**\\n\\n#### rename\\n\\nRenames an active experiment. Returns an error if the experiment is inactive.\\n\\n    \\n    \\n    mlflow experiments rename [OPTIONS]\\n    \\n\\nOptions\\n\\n-x, \\\\--experiment-id <experiment_id>\\n    \\n\\n**Required**\\n\\n\\\\--new-name <new_name>\\n\\n    \\n\\n**Required**\\n\\n#### restore\\n\\nRestore a deleted experiment.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 21503, \"end_char_idx\": 23877, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/cli.html","doc_id":"https://mlflow.org/docs/latest/cli.html","ref_doc_id":"https://mlflow.org/docs/latest/cli.html"}
{"_node_content":"{\"id_\": \"5ee252a6-077f-4c4d-9e93-c371086578d7\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/cli.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"2a8ac9b19cbddee491423c178812937f540f6dfd863499d20a2eb0461839b388\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"f84d5e14-5642-4a48-b954-36e6ae6cb7f3\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"cf88fb061b6a43f0b3119c6683c48b8f3e198541fe9cbe073706873cf05384ec\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"28db1a39-45c1-4e58-9a08-bfaf8b1745e3\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"9d3d467c5f6e37b98884625f569420863537aea1c18772a563f8295d041d4b5e\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"Experiments marked for deletion can be restored using `restore` command,\\nunless they are permanently deleted.\\n\\nSpecific implementation of deletion is dependent on backend stores.\\n`FileStore` moves experiments marked for deletion under a `.trash` folder\\nunder the main folder used to instantiate `FileStore`. Experiments marked for\\ndeletion can be permanently deleted by clearing the `.trash` folder. It is\\nrecommended to use a `cron` job or an alternate workflow mechanism to clear\\n`.trash` folder.\\n\\n    \\n    \\n    mlflow experiments delete [OPTIONS]\\n    \\n\\nOptions\\n\\n-x, \\\\--experiment-id <experiment_id>\\n    \\n\\n**Required**\\n\\n#### rename\\n\\nRenames an active experiment. Returns an error if the experiment is inactive.\\n\\n    \\n    \\n    mlflow experiments rename [OPTIONS]\\n    \\n\\nOptions\\n\\n-x, \\\\--experiment-id <experiment_id>\\n    \\n\\n**Required**\\n\\n\\\\--new-name <new_name>\\n\\n    \\n\\n**Required**\\n\\n#### restore\\n\\nRestore a deleted experiment. This also applies to experiment\\u00e2\\u0080\\u0099s metadata,\\nruns and associated data. The command throws an error if the experiment is\\nalready active, cannot be found, or permanently deleted.\\n\\n    \\n    \\n    mlflow experiments restore [OPTIONS]\\n    \\n\\nOptions\\n\\n-x, \\\\--experiment-id <experiment_id>\\n    \\n\\n**Required**\\n\\n#### search\\n\\nSearch for experiments in the configured tracking server.\\n\\n    \\n    \\n    mlflow experiments search [OPTIONS]\\n    \\n\\nOptions\\n\\n-v, \\\\--view <view>\\n    \\n\\nSelect view type for experiments. Valid view types are \\u00e2\\u0080\\u0098active_only\\u00e2\\u0080\\u0099\\n(default), \\u00e2\\u0080\\u0098deleted_only\\u00e2\\u0080\\u0099, and \\u00e2\\u0080\\u0098all\\u00e2\\u0080\\u0099.\\n\\n### gateway\\n\\nManage the MLflow Gateway service\\n\\n    \\n    \\n    mlflow gateway [OPTIONS] COMMAND [ARGS]...\\n    \\n\\n#### start\\n\\nStart the MLflow Gateway service\\n\\n    \\n    \\n    mlflow gateway start [OPTIONS]\\n    \\n\\nOptions\\n\\n\\\\--config-path <config_path>\\n\\n    \\n\\n**Required** The path to the gateway configuration file.\\n\\n\\\\--host <host>\\n\\n    \\n\\nThe network address to listen on (default: 127.0.0.1).\\n\\n\\\\--port <port>\\n\\n    \\n\\nThe port to listen on (default: 5000).\\n\\n\\\\--workers <workers>\\n\\n    \\n\\nThe number of workers.\\n\\nEnvironment variables\\n\\nMLFLOW_GATEWAY_CONFIG\\n\\n    \\n\\n> Provide a default for `--config-path`\\n\\n### gc\\n\\nPermanently delete runs in the deleted lifecycle stage from the specified\\nbackend store. This command deletes all artifacts and metadata associated with\\nthe specified runs.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 22954, \"end_char_idx\": 25238, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/cli.html","doc_id":"https://mlflow.org/docs/latest/cli.html","ref_doc_id":"https://mlflow.org/docs/latest/cli.html"}
{"_node_content":"{\"id_\": \"28db1a39-45c1-4e58-9a08-bfaf8b1745e3\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/cli.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"2a8ac9b19cbddee491423c178812937f540f6dfd863499d20a2eb0461839b388\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"5ee252a6-077f-4c4d-9e93-c371086578d7\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"f5e1acc1ccc61d2120d261972a740c141e79c5508f31171f76aab46a24aba0c3\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"970d664e-da12-4219-bd6d-f287f0389268\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"0ebef6813f350c346c16120c4a73f0ee46be601cb0a1bf96059c88d579032828\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"### gateway\\n\\nManage the MLflow Gateway service\\n\\n    \\n    \\n    mlflow gateway [OPTIONS] COMMAND [ARGS]...\\n    \\n\\n#### start\\n\\nStart the MLflow Gateway service\\n\\n    \\n    \\n    mlflow gateway start [OPTIONS]\\n    \\n\\nOptions\\n\\n\\\\--config-path <config_path>\\n\\n    \\n\\n**Required** The path to the gateway configuration file.\\n\\n\\\\--host <host>\\n\\n    \\n\\nThe network address to listen on (default: 127.0.0.1).\\n\\n\\\\--port <port>\\n\\n    \\n\\nThe port to listen on (default: 5000).\\n\\n\\\\--workers <workers>\\n\\n    \\n\\nThe number of workers.\\n\\nEnvironment variables\\n\\nMLFLOW_GATEWAY_CONFIG\\n\\n    \\n\\n> Provide a default for `--config-path`\\n\\n### gc\\n\\nPermanently delete runs in the deleted lifecycle stage from the specified\\nbackend store. This command deletes all artifacts and metadata associated with\\nthe specified runs. If the provided artifact URL is invalid, the artifact\\ndeletion will be bypassed, and the gc process will continue.\\n\\nAttention\\n\\nIf you are running an MLflow tracking server with artifact proxying enabled,\\nyou **must** set the `MLFLOW_TRACKING_URI` environment variable before running\\nthis command. Otherwise, the `gc` command will not be able to resolve artifact\\nURIs and will not be able to delete the associated artifacts.\\n\\n    \\n    \\n    mlflow gc [OPTIONS]\\n    \\n\\nOptions\\n\\n\\\\--older-than <older_than>\\n\\n    \\n\\nOptional. Remove run(s) older than the specified time limit. Specify a string\\nin #d#h#m#s format. Float values are also supported. For example: \\u00e2\\u0080\\u0093older-\\nthan 1d2h3m4s, \\u00e2\\u0080\\u0093older-than 1.2d3h4m5s\\n\\n\\\\--backend-store-uri <PATH>\\n\\n    \\n\\nURI of the backend store from which to delete runs. Acceptable URIs are\\nSQLAlchemy-compatible database connection strings (e.g.\\n\\u00e2\\u0080\\u0098sqlite:///path/to/file.db\\u00e2\\u0080\\u0099) or local filesystem URIs (e.g.\\n\\u00e2\\u0080\\u0098file:///absolute/path/to/directory\\u00e2\\u0080\\u0099). By default, data will be deleted\\nfrom the ./mlruns directory.\\n\\n\\\\--artifacts-destination <URI>\\n\\n    \\n\\nThe base artifact location from which to resolve artifact upload/download/list\\nrequests (e.g. \\u00e2\\u0080\\u0098s3://my-bucket\\u00e2\\u0080\\u0099).\", \"mimetype\": \"text/plain\", \"start_char_idx\": 24462, \"end_char_idx\": 26429, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/cli.html","doc_id":"https://mlflow.org/docs/latest/cli.html","ref_doc_id":"https://mlflow.org/docs/latest/cli.html"}
{"_node_content":"{\"id_\": \"970d664e-da12-4219-bd6d-f287f0389268\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/cli.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"2a8ac9b19cbddee491423c178812937f540f6dfd863499d20a2eb0461839b388\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"28db1a39-45c1-4e58-9a08-bfaf8b1745e3\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"9d3d467c5f6e37b98884625f569420863537aea1c18772a563f8295d041d4b5e\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"f18b4db5-dc9b-44a4-aaea-6500bbfe2222\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"fc2d8f82649bead7ffac8db89da74487eb951ca5d251c5c9a3bc3fe39d26c6a3\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"Remove run(s) older than the specified time limit. Specify a string\\nin #d#h#m#s format. Float values are also supported. For example: \\u00e2\\u0080\\u0093older-\\nthan 1d2h3m4s, \\u00e2\\u0080\\u0093older-than 1.2d3h4m5s\\n\\n\\\\--backend-store-uri <PATH>\\n\\n    \\n\\nURI of the backend store from which to delete runs. Acceptable URIs are\\nSQLAlchemy-compatible database connection strings (e.g.\\n\\u00e2\\u0080\\u0098sqlite:///path/to/file.db\\u00e2\\u0080\\u0099) or local filesystem URIs (e.g.\\n\\u00e2\\u0080\\u0098file:///absolute/path/to/directory\\u00e2\\u0080\\u0099). By default, data will be deleted\\nfrom the ./mlruns directory.\\n\\n\\\\--artifacts-destination <URI>\\n\\n    \\n\\nThe base artifact location from which to resolve artifact upload/download/list\\nrequests (e.g. \\u00e2\\u0080\\u0098s3://my-bucket\\u00e2\\u0080\\u0099). This option only applies when the\\ntracking server is configured to stream artifacts and the experiment\\u00e2\\u0080\\u0099s\\nartifact root location is http or mlflow-artifacts URI. Otherwise, the default\\nartifact location will be used.\\n\\n\\\\--run-ids <run_ids>\\n\\n    \\n\\nOptional comma separated list of runs to be permanently deleted. If run ids\\nare not specified, data is removed for all runs in the deleted lifecycle\\nstage.\\n\\n\\\\--experiment-ids <experiment_ids>\\n\\n    \\n\\nOptional comma separated list of experiments to be permanently deleted\\nincluding all of their associated runs. If experiment ids are not specified,\\ndata is removed for all experiments in the deleted lifecycle stage.\\n\\n\\\\--tracking-uri <tracking_uri>\\n\\n    \\n\\nTracking URI to use for deleting \\u00e2\\u0080\\u0098deleted\\u00e2\\u0080\\u0099 runs e.g.\\n<http://127.0.0.1:8080>\\n\\nEnvironment variables\\n\\nMLFLOW_ARTIFACTS_DESTINATION\\n\\n    \\n\\n> Provide a default for `--artifacts-destination`\\n\\n### models\\n\\nDeploy MLflow models locally.\\n\\nTo deploy a model associated with a run on a tracking server, set the\\nMLFLOW_TRACKING_URI environment variable to the URL of the desired server.\\n\\n    \\n    \\n    mlflow models [OPTIONS] COMMAND [ARGS]...\\n    \\n\\n#### build-docker\\n\\nBuilds a Docker image whose default entrypoint serves an MLflow model at port\\n8080, using the python_function flavor. The container serves the model\\nreferenced by `--model-uri`, if specified when `build-docker` is called.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 25757, \"end_char_idx\": 27814, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/cli.html","doc_id":"https://mlflow.org/docs/latest/cli.html","ref_doc_id":"https://mlflow.org/docs/latest/cli.html"}
{"_node_content":"{\"id_\": \"f18b4db5-dc9b-44a4-aaea-6500bbfe2222\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/cli.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"2a8ac9b19cbddee491423c178812937f540f6dfd863499d20a2eb0461839b388\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"970d664e-da12-4219-bd6d-f287f0389268\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"0ebef6813f350c346c16120c4a73f0ee46be601cb0a1bf96059c88d579032828\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"473ab651-6d2b-476e-bb7d-9b1550440801\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"27bfeae19b6cdbf66b7096cd819d10450f1bd40371cbff9ea4d23cea1ff22796\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"If experiment ids are not specified,\\ndata is removed for all experiments in the deleted lifecycle stage.\\n\\n\\\\--tracking-uri <tracking_uri>\\n\\n    \\n\\nTracking URI to use for deleting \\u00e2\\u0080\\u0098deleted\\u00e2\\u0080\\u0099 runs e.g.\\n<http://127.0.0.1:8080>\\n\\nEnvironment variables\\n\\nMLFLOW_ARTIFACTS_DESTINATION\\n\\n    \\n\\n> Provide a default for `--artifacts-destination`\\n\\n### models\\n\\nDeploy MLflow models locally.\\n\\nTo deploy a model associated with a run on a tracking server, set the\\nMLFLOW_TRACKING_URI environment variable to the URL of the desired server.\\n\\n    \\n    \\n    mlflow models [OPTIONS] COMMAND [ARGS]...\\n    \\n\\n#### build-docker\\n\\nBuilds a Docker image whose default entrypoint serves an MLflow model at port\\n8080, using the python_function flavor. The container serves the model\\nreferenced by `--model-uri`, if specified when `build-docker` is called. If\\n`--model-uri` is not specified when build_docker is called, an MLflow Model\\ndirectory must be mounted as a volume into the /opt/ml/model directory in the\\ncontainer.\\n\\nBuilding a Docker image with `--model-uri`:\\n\\n    \\n    \\n    # Build a Docker image named 'my-image-name' that serves the model from run 'some-run-uuid'\\n    # at run-relative artifact path 'my-model'\\n    mlflow models build-docker --model-uri \\\"runs:/some-run-uuid/my-model\\\" --name \\\"my-image-name\\\"\\n    # Serve the model\\n    docker run -p 5001:8080 \\\"my-image-name\\\"\\n    \\n\\nBuilding a Docker image without `--model-uri`:\\n\\n    \\n    \\n    # Build a generic Docker image named 'my-image-name'\\n    mlflow models build-docker --name \\\"my-image-name\\\"\\n    # Mount the model stored in '/local/path/to/artifacts/model' and serve it\\n    docker run --rm -p 5001:8080 -v /local/path/to/artifacts/model:/opt/ml/model \\\"my-image-name\\\"\\n    \\n\\nImportant\\n\\nSince MLflow 2.10.1, the Docker image built with `--model-uri` does **not\\ninstall Java** for improved performance, unless the model flavor is one of\\n`[\\\"johnsnowlabs\\\", \\\"h2o\\\", \\\"spark\\\"]`. If you need to install Java for other\\nflavors, e.g.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 26987, \"end_char_idx\": 28948, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/cli.html","doc_id":"https://mlflow.org/docs/latest/cli.html","ref_doc_id":"https://mlflow.org/docs/latest/cli.html"}
{"_node_content":"{\"id_\": \"473ab651-6d2b-476e-bb7d-9b1550440801\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/cli.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"2a8ac9b19cbddee491423c178812937f540f6dfd863499d20a2eb0461839b388\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"f18b4db5-dc9b-44a4-aaea-6500bbfe2222\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"fc2d8f82649bead7ffac8db89da74487eb951ca5d251c5c9a3bc3fe39d26c6a3\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"fe81c329-c689-473e-aea4-36f8cf6cccdc\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"233c6c033184673c77d6e9c4391b4732590df9510cb1e2e28d515f97725822d4\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"If you need to install Java for other\\nflavors, e.g. custom Python model that uses SparkML, please specify the\\n`--install-java` flag to enforce Java installation.\\n\\nNB: by default, the container will start nginx and uvicorn processes. If you\\ndon\\u00e2\\u0080\\u0099t need the nginx process to be started (for instance if you deploy your\\ncontainer to Google Cloud Run), you can disable it via the DISABLE_NGINX\\nenvironment variable:\\n\\n    \\n    \\n    docker run -p 5001:8080 -e DISABLE_NGINX=true \\\"my-image-name\\\"\\n    \\n\\nBy default, the number of uvicorn workers is set to CPU count. If you want to\\nset a custom number of workers, you can set the MLFLOW_MODELS_WORKERS\\nenvironment variable:\\n\\n    \\n    \\n    docker run -p 5001:8080 -e MLFLOW_MODELS_WORKERS=4 \\\"my-image-name\\\"\\n    \\n\\nSee <https://www.mlflow.org/docs/latest/python_api/mlflow.pyfunc.html> for\\nmore information on the \\u00e2\\u0080\\u0098python_function\\u00e2\\u0080\\u0099 flavor.\\n\\n    \\n    \\n    mlflow models build-docker [OPTIONS]\\n    \\n\\nOptions\\n\\n-m, \\\\--model-uri <URI>\\n    \\n\\n[Optional] URI to the model. A local path, a \\u00e2\\u0080\\u0098runs:/\\u00e2\\u0080\\u0099 URI, or a remote\\nstorage URI (e.g., an \\u00e2\\u0080\\u0098s3://\\u00e2\\u0080\\u0099 URI). For more information about supported\\nremote URIs for model artifacts, see\\nhttps://mlflow.org/docs/latest/tracking.html#artifact-stores\\n\\n-n, \\\\--name <name>\\n    \\n\\nName to use for built image\\n\\n\\\\--env-manager <env_manager>\\n\\n    \\n\\nIf specified, create an environment for MLmodel using the specified\\nenvironment manager. The following values are supported:\\n\\n\\\\- local: use the local environment\\n\\n\\\\- virtualenv: use virtualenv (and pyenv for Python version management)\\n\\n\\\\- conda: use conda\\n\\nIf unspecified, default to virtualenv.\\n\\n\\\\--mlflow-home <PATH>\\n\\n    \\n\\nPath to local clone of MLflow project. Use for development only.\\n\\n\\\\--install-java <install_java>\\n\\n    \\n\\nInstalls Java in the image if needed. Default is None, allowing MLflow to\\ndetermine installation. Flavors requiring Java, such as Spark, enable this\\nautomatically. Note: This option only works with the UBUNTU base image; Python\\nbase images do not support Java installation.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 28897, \"end_char_idx\": 30920, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/cli.html","doc_id":"https://mlflow.org/docs/latest/cli.html","ref_doc_id":"https://mlflow.org/docs/latest/cli.html"}
{"_node_content":"{\"id_\": \"fe81c329-c689-473e-aea4-36f8cf6cccdc\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/cli.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"2a8ac9b19cbddee491423c178812937f540f6dfd863499d20a2eb0461839b388\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"473ab651-6d2b-476e-bb7d-9b1550440801\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"27bfeae19b6cdbf66b7096cd819d10450f1bd40371cbff9ea4d23cea1ff22796\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"ac5a1bdb-0f44-4a74-b1d3-2be0f33366e4\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"6c798039a03fee14decba0b091e7c4f9315018d011b17f7c6c7e0da71294dca8\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"The following values are supported:\\n\\n\\\\- local: use the local environment\\n\\n\\\\- virtualenv: use virtualenv (and pyenv for Python version management)\\n\\n\\\\- conda: use conda\\n\\nIf unspecified, default to virtualenv.\\n\\n\\\\--mlflow-home <PATH>\\n\\n    \\n\\nPath to local clone of MLflow project. Use for development only.\\n\\n\\\\--install-java <install_java>\\n\\n    \\n\\nInstalls Java in the image if needed. Default is None, allowing MLflow to\\ndetermine installation. Flavors requiring Java, such as Spark, enable this\\nautomatically. Note: This option only works with the UBUNTU base image; Python\\nbase images do not support Java installation.\\n\\n\\\\--install-mlflow\\n\\n    \\n\\nIf specified and there is a conda or virtualenv environment to be activated\\nmlflow will be installed into the environment after it has been activated. The\\nversion of installed mlflow will be the same as the one used to invoke this\\ncommand.\\n\\n\\\\--enable-mlserver\\n\\n    \\n\\nEnable serving with MLServer through the v2 inference protocol. You can use\\nenvironment variables to configure MLServer. (See\\n<https://mlserver.readthedocs.io/en/latest/reference/settings.html>)\\n\\n#### generate-dockerfile\\n\\nGenerates a directory with Dockerfile whose default entrypoint serves an\\nMLflow model at port 8080 using the python_function flavor. The generated\\nDockerfile is written to the specified output directory, along with the model\\n(if specified). This Dockerfile defines an image that is equivalent to the one\\nproduced by `mlflow models build-docker`.\\n\\n    \\n    \\n    mlflow models generate-dockerfile [OPTIONS]\\n    \\n\\nOptions\\n\\n-m, \\\\--model-uri <URI>\\n    \\n\\n[Optional] URI to the model. A local path, a \\u00e2\\u0080\\u0098runs:/\\u00e2\\u0080\\u0099 URI, or a remote\\nstorage URI (e.g., an \\u00e2\\u0080\\u0098s3://\\u00e2\\u0080\\u0099 URI). For more information about supported\\nremote URIs for model artifacts, see\\nhttps://mlflow.org/docs/latest/tracking.html#artifact-stores\\n\\n-d, \\\\--output-directory <output_directory>\\n    \\n\\nOutput directory where the generated Dockerfile is stored.\\n\\n\\\\--env-manager <env_manager>\\n\\n    \\n\\nIf specified, create an environment for MLmodel using the specified\\nenvironment manager.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 30306, \"end_char_idx\": 32369, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/cli.html","doc_id":"https://mlflow.org/docs/latest/cli.html","ref_doc_id":"https://mlflow.org/docs/latest/cli.html"}
{"_node_content":"{\"id_\": \"ac5a1bdb-0f44-4a74-b1d3-2be0f33366e4\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/cli.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"2a8ac9b19cbddee491423c178812937f540f6dfd863499d20a2eb0461839b388\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"fe81c329-c689-473e-aea4-36f8cf6cccdc\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"233c6c033184673c77d6e9c4391b4732590df9510cb1e2e28d515f97725822d4\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"12b643b9-a52e-4b1b-8313-2012c4c18ff6\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"5cde35c22dcb9860b0a9579375aa17b506a13694f8f67b4ad9b6c8a537be92ac\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"The generated\\nDockerfile is written to the specified output directory, along with the model\\n(if specified). This Dockerfile defines an image that is equivalent to the one\\nproduced by `mlflow models build-docker`.\\n\\n    \\n    \\n    mlflow models generate-dockerfile [OPTIONS]\\n    \\n\\nOptions\\n\\n-m, \\\\--model-uri <URI>\\n    \\n\\n[Optional] URI to the model. A local path, a \\u00e2\\u0080\\u0098runs:/\\u00e2\\u0080\\u0099 URI, or a remote\\nstorage URI (e.g., an \\u00e2\\u0080\\u0098s3://\\u00e2\\u0080\\u0099 URI). For more information about supported\\nremote URIs for model artifacts, see\\nhttps://mlflow.org/docs/latest/tracking.html#artifact-stores\\n\\n-d, \\\\--output-directory <output_directory>\\n    \\n\\nOutput directory where the generated Dockerfile is stored.\\n\\n\\\\--env-manager <env_manager>\\n\\n    \\n\\nIf specified, create an environment for MLmodel using the specified\\nenvironment manager. The following values are supported:\\n\\n\\\\- local: use the local environment\\n\\n\\\\- virtualenv: use virtualenv (and pyenv for Python version management)\\n\\n\\\\- conda: use conda\\n\\nIf unspecified, default to None, then MLflow will automatically pick the env\\nmanager based on the model\\u00e2\\u0080\\u0099s flavor configuration. If model-uri is\\nspecified: if python version is specified in the flavor configuration and no\\njava installation is required, then we use local environment. Otherwise we use\\nvirtualenv. If no model-uri is provided, we use virtualenv.\\n\\n\\\\--mlflow-home <PATH>\\n\\n    \\n\\nPath to local clone of MLflow project. Use for development only.\\n\\n\\\\--install-java <install_java>\\n\\n    \\n\\nInstalls Java in the image if needed. Default is None, allowing MLflow to\\ndetermine installation. Flavors requiring Java, such as Spark, enable this\\nautomatically. Note: This option only works with the UBUNTU base image; Python\\nbase images do not support Java installation.\\n\\n\\\\--install-mlflow\\n\\n    \\n\\nIf specified and there is a conda or virtualenv environment to be activated\\nmlflow will be installed into the environment after it has been activated. The\\nversion of installed mlflow will be the same as the one used to invoke this\\ncommand.\\n\\n\\\\--enable-mlserver\\n\\n    \\n\\nEnable serving with MLServer through the v2 inference protocol. You can use\\nenvironment variables to configure MLServer.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 31569, \"end_char_idx\": 33721, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/cli.html","doc_id":"https://mlflow.org/docs/latest/cli.html","ref_doc_id":"https://mlflow.org/docs/latest/cli.html"}
{"_node_content":"{\"id_\": \"12b643b9-a52e-4b1b-8313-2012c4c18ff6\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/cli.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"2a8ac9b19cbddee491423c178812937f540f6dfd863499d20a2eb0461839b388\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"ac5a1bdb-0f44-4a74-b1d3-2be0f33366e4\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"6c798039a03fee14decba0b091e7c4f9315018d011b17f7c6c7e0da71294dca8\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"21e165e9-b235-4597-b0eb-c34fb98aeebc\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"19557b6dbd862259d3e249ce2a77f86ae01c82fdb809d42fc8bd240f0babb930\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"\\\\--mlflow-home <PATH>\\n\\n    \\n\\nPath to local clone of MLflow project. Use for development only.\\n\\n\\\\--install-java <install_java>\\n\\n    \\n\\nInstalls Java in the image if needed. Default is None, allowing MLflow to\\ndetermine installation. Flavors requiring Java, such as Spark, enable this\\nautomatically. Note: This option only works with the UBUNTU base image; Python\\nbase images do not support Java installation.\\n\\n\\\\--install-mlflow\\n\\n    \\n\\nIf specified and there is a conda or virtualenv environment to be activated\\nmlflow will be installed into the environment after it has been activated. The\\nversion of installed mlflow will be the same as the one used to invoke this\\ncommand.\\n\\n\\\\--enable-mlserver\\n\\n    \\n\\nEnable serving with MLServer through the v2 inference protocol. You can use\\nenvironment variables to configure MLServer. (See\\n<https://mlserver.readthedocs.io/en/latest/reference/settings.html>)\\n\\n#### predict\\n\\nGenerate predictions in json format using a saved MLflow model. For\\ninformation about the input data formats accepted by this function, see the\\nfollowing documentation:\\n<https://www.mlflow.org/docs/latest/models.html#built-in-deployment-tools>.\\n\\n    \\n    \\n    mlflow models predict [OPTIONS]\\n    \\n\\nOptions\\n\\n-m, \\\\--model-uri <URI>\\n    \\n\\n**Required** URI to the model. A local path, a \\u00e2\\u0080\\u0098runs:/\\u00e2\\u0080\\u0099 URI, or a remote\\nstorage URI (e.g., an \\u00e2\\u0080\\u0098s3://\\u00e2\\u0080\\u0099 URI). For more information about supported\\nremote URIs for model artifacts, see\\nhttps://mlflow.org/docs/latest/tracking.html#artifact-stores\\n\\n-i, \\\\--input-path <input_path>\\n    \\n\\nCSV containing pandas DataFrame to predict against.\\n\\n-o, \\\\--output-path <output_path>\\n    \\n\\nFile to output results to as json file. If not provided, output to stdout.\\n\\n-t, \\\\--content-type <content_type>\\n    \\n\\nContent type of the input file. Can be one of {\\u00e2\\u0080\\u0098json\\u00e2\\u0080\\u0099, \\u00e2\\u0080\\u0098csv\\u00e2\\u0080\\u0099}.\\n\\n\\\\--env-manager <env_manager>\\n\\n    \\n\\nIf specified, create an environment for MLmodel using the specified\\nenvironment manager.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 32901, \"end_char_idx\": 34842, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/cli.html","doc_id":"https://mlflow.org/docs/latest/cli.html","ref_doc_id":"https://mlflow.org/docs/latest/cli.html"}
{"_node_content":"{\"id_\": \"21e165e9-b235-4597-b0eb-c34fb98aeebc\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/cli.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"2a8ac9b19cbddee491423c178812937f540f6dfd863499d20a2eb0461839b388\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"12b643b9-a52e-4b1b-8313-2012c4c18ff6\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"5cde35c22dcb9860b0a9579375aa17b506a13694f8f67b4ad9b6c8a537be92ac\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"d2822fa3-0534-49f1-851c-7a215e306219\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"96b120a70c1756a6e195e0cdffa7e38e2464dace1b7c92f4a3fcdf25abd749a7\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"A local path, a \\u00e2\\u0080\\u0098runs:/\\u00e2\\u0080\\u0099 URI, or a remote\\nstorage URI (e.g., an \\u00e2\\u0080\\u0098s3://\\u00e2\\u0080\\u0099 URI). For more information about supported\\nremote URIs for model artifacts, see\\nhttps://mlflow.org/docs/latest/tracking.html#artifact-stores\\n\\n-i, \\\\--input-path <input_path>\\n    \\n\\nCSV containing pandas DataFrame to predict against.\\n\\n-o, \\\\--output-path <output_path>\\n    \\n\\nFile to output results to as json file. If not provided, output to stdout.\\n\\n-t, \\\\--content-type <content_type>\\n    \\n\\nContent type of the input file. Can be one of {\\u00e2\\u0080\\u0098json\\u00e2\\u0080\\u0099, \\u00e2\\u0080\\u0098csv\\u00e2\\u0080\\u0099}.\\n\\n\\\\--env-manager <env_manager>\\n\\n    \\n\\nIf specified, create an environment for MLmodel using the specified\\nenvironment manager. The following values are supported:\\n\\n\\\\- local: use the local environment\\n\\n\\\\- virtualenv: use virtualenv (and pyenv for Python version management)\\n\\n\\\\- conda: use conda\\n\\nIf unspecified, default to virtualenv.\\n\\n\\\\--install-mlflow\\n\\n    \\n\\nIf specified and there is a conda or virtualenv environment to be activated\\nmlflow will be installed into the environment after it has been activated. The\\nversion of installed mlflow will be the same as the one used to invoke this\\ncommand.\\n\\n-r, \\\\--pip-requirements-override <pip_requirements_override>\\n    \\n\\nSpecify packages and versions to override the dependencies defined in the\\nmodel. Must be a comma-separated string like x==y,z==a.\\n\\n\\\\--env <env>\\n\\n    \\n\\nExtra environment variables to set when running the model. Must be key value\\npairs, e.g. \\u00e2\\u0080\\u0093env key=value.\\n\\n#### prepare-env\\n\\nPerforms any preparation necessary to predict or serve the model, for example\\ndownloading dependencies or initializing a conda environment. After\\npreparation, calling predict or serve should be fast.\\n\\n    \\n    \\n    mlflow models prepare-env [OPTIONS]\\n    \\n\\nOptions\\n\\n-m, \\\\--model-uri <URI>\\n    \\n\\n**Required** URI to the model. A local path, a \\u00e2\\u0080\\u0098runs:/\\u00e2\\u0080\\u0099 URI, or a remote\\nstorage URI (e.g., an \\u00e2\\u0080\\u0098s3://\\u00e2\\u0080\\u0099 URI).\", \"mimetype\": \"text/plain\", \"start_char_idx\": 34178, \"end_char_idx\": 36072, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/cli.html","doc_id":"https://mlflow.org/docs/latest/cli.html","ref_doc_id":"https://mlflow.org/docs/latest/cli.html"}
{"_node_content":"{\"id_\": \"d2822fa3-0534-49f1-851c-7a215e306219\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/cli.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"2a8ac9b19cbddee491423c178812937f540f6dfd863499d20a2eb0461839b388\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"21e165e9-b235-4597-b0eb-c34fb98aeebc\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"19557b6dbd862259d3e249ce2a77f86ae01c82fdb809d42fc8bd240f0babb930\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"1d2b4afe-25e2-4820-ae13-fa9a3b0a0108\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"ca8c2a3ff667610106b4087124d2a1d3a94a3080eac5c96760327f8285f5d3fd\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"-r, \\\\--pip-requirements-override <pip_requirements_override>\\n    \\n\\nSpecify packages and versions to override the dependencies defined in the\\nmodel. Must be a comma-separated string like x==y,z==a.\\n\\n\\\\--env <env>\\n\\n    \\n\\nExtra environment variables to set when running the model. Must be key value\\npairs, e.g. \\u00e2\\u0080\\u0093env key=value.\\n\\n#### prepare-env\\n\\nPerforms any preparation necessary to predict or serve the model, for example\\ndownloading dependencies or initializing a conda environment. After\\npreparation, calling predict or serve should be fast.\\n\\n    \\n    \\n    mlflow models prepare-env [OPTIONS]\\n    \\n\\nOptions\\n\\n-m, \\\\--model-uri <URI>\\n    \\n\\n**Required** URI to the model. A local path, a \\u00e2\\u0080\\u0098runs:/\\u00e2\\u0080\\u0099 URI, or a remote\\nstorage URI (e.g., an \\u00e2\\u0080\\u0098s3://\\u00e2\\u0080\\u0099 URI). For more information about supported\\nremote URIs for model artifacts, see\\nhttps://mlflow.org/docs/latest/tracking.html#artifact-stores\\n\\n\\\\--env-manager <env_manager>\\n\\n    \\n\\nIf specified, create an environment for MLmodel using the specified\\nenvironment manager. The following values are supported:\\n\\n\\\\- local: use the local environment\\n\\n\\\\- virtualenv: use virtualenv (and pyenv for Python version management)\\n\\n\\\\- conda: use conda\\n\\nIf unspecified, default to virtualenv.\\n\\n\\\\--install-mlflow\\n\\n    \\n\\nIf specified and there is a conda or virtualenv environment to be activated\\nmlflow will be installed into the environment after it has been activated. The\\nversion of installed mlflow will be the same as the one used to invoke this\\ncommand.\\n\\n#### serve\\n\\nServe a model saved with MLflow by launching a webserver on the specified host\\nand port. The command supports models with the `python_function` or `crate` (R\\nFunction) flavor. For information about the input data formats accepted by the\\nwebserver, see the following documentation:\\n<https://www.mlflow.org/docs/latest/models.html#built-in-deployment-tools>.\\n\\nWarning\\n\\nModels built using MLflow 1.x will require adjustments to the endpoint request\\npayload if executed in an environment that has MLflow 2.x installed. In 1.x, a\\nrequest payload was in the format: `{'columns': [str], 'data': [[...]]}`.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 35317, \"end_char_idx\": 37418, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/cli.html","doc_id":"https://mlflow.org/docs/latest/cli.html","ref_doc_id":"https://mlflow.org/docs/latest/cli.html"}
{"_node_content":"{\"id_\": \"1d2b4afe-25e2-4820-ae13-fa9a3b0a0108\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/cli.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"2a8ac9b19cbddee491423c178812937f540f6dfd863499d20a2eb0461839b388\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"d2822fa3-0534-49f1-851c-7a215e306219\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"96b120a70c1756a6e195e0cdffa7e38e2464dace1b7c92f4a3fcdf25abd749a7\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"a4cbb763-3f16-4eb4-baa6-4a9d2f9444ee\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"7ffe0eb29eb9c85d72531fc585f9571edc6ac80f27df3086316e4fc4bd9f2c54\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"The\\nversion of installed mlflow will be the same as the one used to invoke this\\ncommand.\\n\\n#### serve\\n\\nServe a model saved with MLflow by launching a webserver on the specified host\\nand port. The command supports models with the `python_function` or `crate` (R\\nFunction) flavor. For information about the input data formats accepted by the\\nwebserver, see the following documentation:\\n<https://www.mlflow.org/docs/latest/models.html#built-in-deployment-tools>.\\n\\nWarning\\n\\nModels built using MLflow 1.x will require adjustments to the endpoint request\\npayload if executed in an environment that has MLflow 2.x installed. In 1.x, a\\nrequest payload was in the format: `{'columns': [str], 'data': [[...]]}`. 2.x\\nmodels require payloads that are defined by the structural-defining keys of\\neither `dataframe_split`, `instances`, `inputs` or `dataframe_records`. See\\nthe examples below for demonstrations of the changes to the invocation API\\nendpoint in 2.0.\\n\\nNote\\n\\nRequests made in pandas DataFrame structures can be made in either split or\\nrecords oriented formats. See\\n<https://pandas.pydata.org/docs/reference/api/pandas.DataFrame.to_json.html>\\nfor detailed information on orientation formats for converting a pandas\\nDataFrame to json.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 36718, \"end_char_idx\": 37947, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/cli.html","doc_id":"https://mlflow.org/docs/latest/cli.html","ref_doc_id":"https://mlflow.org/docs/latest/cli.html"}
{"_node_content":"{\"id_\": \"a4cbb763-3f16-4eb4-baa6-4a9d2f9444ee\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/cli.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"2a8ac9b19cbddee491423c178812937f540f6dfd863499d20a2eb0461839b388\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"1d2b4afe-25e2-4820-ae13-fa9a3b0a0108\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"ca8c2a3ff667610106b4087124d2a1d3a94a3080eac5c96760327f8285f5d3fd\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"6a159eef-f978-4d9e-8dd9-f5aabb9bad21\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"dd9d6d419a1687cf98d21d7fda5917e69c56d6853ce7555715c6cfb3b63fd9a0\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"Warning\\n\\nModels built using MLflow 1.x will require adjustments to the endpoint request\\npayload if executed in an environment that has MLflow 2.x installed. In 1.x, a\\nrequest payload was in the format: `{'columns': [str], 'data': [[...]]}`. 2.x\\nmodels require payloads that are defined by the structural-defining keys of\\neither `dataframe_split`, `instances`, `inputs` or `dataframe_records`. See\\nthe examples below for demonstrations of the changes to the invocation API\\nendpoint in 2.0.\\n\\nNote\\n\\nRequests made in pandas DataFrame structures can be made in either split or\\nrecords oriented formats. See\\n<https://pandas.pydata.org/docs/reference/api/pandas.DataFrame.to_json.html>\\nfor detailed information on orientation formats for converting a pandas\\nDataFrame to json.\\n\\nExample:\\n\\n    \\n    \\n    $ mlflow models serve -m runs:/my-run-id/model-path &\\n    \\n    # records orientation input format for serializing a pandas DataFrame\\n    $ curl http://127.0.0.1:5000/invocations -H 'Content-Type: application/json' -d '{\\n        \\\"dataframe_records\\\": [{\\\"a\\\":1, \\\"b\\\":2}, {\\\"a\\\":3, \\\"b\\\":4}, {\\\"a\\\":5, \\\"b\\\":6}]\\n    }'\\n    \\n    # split orientation input format for serializing a pandas DataFrame\\n    $ curl http://127.0.0.1:5000/invocations -H 'Content-Type: application/json' -d '{\\n        \\\"dataframe_split\\\": {\\\"columns\\\": [\\\"a\\\", \\\"b\\\"],\\n                            \\\"index\\\": [0, 1, 2],\\n                            \\\"data\\\": [[1, 2], [3, 4], [5, 6]]}\\n    }'\\n    \\n    # inputs format for List submission of array, tensor, or DataFrame data\\n    $ curl http://127.0.0.1:5000/invocations -H 'Content-Type: application/json' -d '{\\n        \\\"inputs\\\": [[1, 2], [3, 4], [5, 6]]\\n    }'\\n    \\n    # instances format for submission of Tensor data\\n    curl http://127.0.0.1:5000/invocations -H 'Content-Type: application/json' -d '{\\n        \\\"instances\\\": [\\n            {\\\"a\\\": \\\"t1\\\", \\\"b\\\": [1, 2, 3]},\\n            {\\\"a\\\": \\\"t2\\\", \\\"b\\\": [4, 5, 6]},\\n            {\\\"a\\\": \\\"t3\\\", \\\"b\\\": [7, 8, 9]}\\n        ]\\n    }'\\n    \\n    \\n    \\n    mlflow models serve [OPTIONS]\\n    \\n\\nOptions\\n\\n-m, \\\\--model-uri <URI>\\n    \\n\\n**Required** URI to the model.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 37178, \"end_char_idx\": 39256, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/cli.html","doc_id":"https://mlflow.org/docs/latest/cli.html","ref_doc_id":"https://mlflow.org/docs/latest/cli.html"}
{"_node_content":"{\"id_\": \"6a159eef-f978-4d9e-8dd9-f5aabb9bad21\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/cli.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"2a8ac9b19cbddee491423c178812937f540f6dfd863499d20a2eb0461839b388\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"a4cbb763-3f16-4eb4-baa6-4a9d2f9444ee\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"7ffe0eb29eb9c85d72531fc585f9571edc6ac80f27df3086316e4fc4bd9f2c54\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"19f9ab66-b02c-4148-b9e0-bec31d12e39a\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"d4f850719b7f4bc3218a4fddbceb648d757a331e7c50143878779c8ad5a07b10\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"A local path, a \\u00e2\\u0080\\u0098runs:/\\u00e2\\u0080\\u0099 URI, or a remote\\nstorage URI (e.g., an \\u00e2\\u0080\\u0098s3://\\u00e2\\u0080\\u0099 URI). For more information about supported\\nremote URIs for model artifacts, see\\nhttps://mlflow.org/docs/latest/tracking.html#artifact-stores\\n\\n-p, \\\\--port <port>\\n    \\n\\nThe port to listen on (default: 5000).\\n\\n-h, \\\\--host <HOST>\\n    \\n\\nThe network address to listen on (default: 127.0.0.1). Use 0.0.0.0 to bind to\\nall addresses if you want to access the tracking server from other machines.\\n\\n-t, \\\\--timeout <timeout>\\n    \\n\\nTimeout in seconds to serve a request (default: 60).\\n\\n-w, \\\\--workers <workers>\\n    \\n\\nNumber of uvicorn workers to handle requests when serving mlflow models\\n(default: 1).\\n\\n\\\\--env-manager <env_manager>\\n\\n    \\n\\nIf specified, create an environment for MLmodel using the specified\\nenvironment manager. The following values are supported:\\n\\n\\\\- local: use the local environment\\n\\n\\\\- virtualenv: use virtualenv (and pyenv for Python version management)\\n\\n\\\\- conda: use conda\\n\\nIf unspecified, default to virtualenv.\\n\\n\\\\--no-conda\\n\\n    \\n\\nIf specified, use local environment.\\n\\n\\\\--install-mlflow\\n\\n    \\n\\nIf specified and there is a conda or virtualenv environment to be activated\\nmlflow will be installed into the environment after it has been activated. The\\nversion of installed mlflow will be the same as the one used to invoke this\\ncommand.\\n\\n\\\\--enable-mlserver\\n\\n    \\n\\nEnable serving with MLServer through the v2 inference protocol. You can use\\nenvironment variables to configure MLServer. (See\\n<https://mlserver.readthedocs.io/en/latest/reference/settings.html>)\\n\\nEnvironment variables\\n\\nMLFLOW_PORT\\n\\n    \\n\\n> Provide a default for `--port`\\n\\nMLFLOW_HOST\\n\\n    \\n\\n> Provide a default for `--host`\\n\\nMLFLOW_SCORING_SERVER_REQUEST_TIMEOUT\\n\\n    \\n\\n> Provide a default for `--timeout`\\n\\nMLFLOW_MODELS_WORKERS\\n\\n    \\n\\n> Provide a default for `--workers`\\n\\n#### update-pip-requirements\\n\\nAdd or remove requirements from a model\\u00e2\\u0080\\u0099s conda.yaml and requirements.txt\\nfiles.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 39257, \"end_char_idx\": 41195, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/cli.html","doc_id":"https://mlflow.org/docs/latest/cli.html","ref_doc_id":"https://mlflow.org/docs/latest/cli.html"}
{"_node_content":"{\"id_\": \"19f9ab66-b02c-4148-b9e0-bec31d12e39a\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/cli.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"2a8ac9b19cbddee491423c178812937f540f6dfd863499d20a2eb0461839b388\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"6a159eef-f978-4d9e-8dd9-f5aabb9bad21\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"dd9d6d419a1687cf98d21d7fda5917e69c56d6853ce7555715c6cfb3b63fd9a0\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"52fed91b-1a2e-4072-8fad-3d58d316a80b\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"0275815d21bd81a76ca793a7596d44f53cb86fec9e0dbc1eb8bf362f4792a07a\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"The\\nversion of installed mlflow will be the same as the one used to invoke this\\ncommand.\\n\\n\\\\--enable-mlserver\\n\\n    \\n\\nEnable serving with MLServer through the v2 inference protocol. You can use\\nenvironment variables to configure MLServer. (See\\n<https://mlserver.readthedocs.io/en/latest/reference/settings.html>)\\n\\nEnvironment variables\\n\\nMLFLOW_PORT\\n\\n    \\n\\n> Provide a default for `--port`\\n\\nMLFLOW_HOST\\n\\n    \\n\\n> Provide a default for `--host`\\n\\nMLFLOW_SCORING_SERVER_REQUEST_TIMEOUT\\n\\n    \\n\\n> Provide a default for `--timeout`\\n\\nMLFLOW_MODELS_WORKERS\\n\\n    \\n\\n> Provide a default for `--workers`\\n\\n#### update-pip-requirements\\n\\nAdd or remove requirements from a model\\u00e2\\u0080\\u0099s conda.yaml and requirements.txt\\nfiles. If using a remote tracking server, please make sure to set the\\nMLFLOW_TRACKING_URI environment variable to the URL of the desired server.\\n\\nREQUIREMENT_STRINGS is a list of pip requirements specifiers. See below for\\nexamples.\\n\\nSample usage:\\n\\n    \\n    \\n    # Add requirements using the model's \\\"runs:/\\\" URI\\n    \\n    mlflow models update-pip-requirements -m runs:/<run_id>/<model_path> \\\\\\n        add \\\"pandas==1.0.0\\\" \\\"scikit-learn\\\" \\\"mlflow >= 2.8, != 2.9.0\\\"\\n    \\n    # Remove requirements from a local model\\n    \\n    mlflow models update-pip-requirements -m /path/to/local/model \\\\\\n        remove \\\"torchvision\\\" \\\"pydantic\\\"\\n    \\n\\nNote that model registry URIs (i.e. URIs in the form `models:/`) are not\\nsupported, as artifacts in the model registry are intended to be read-only.\\nEditing requirements is read-only artifact repositories is also not supported.\\n\\nIf adding requirements, the function will overwrite any existing requirements\\nthat overlap, or else append the new requirements to the existing list.\\n\\nIf removing requirements, the function will ignore any version specifiers, and\\nremove all the specified package names. Any requirements that are not found in\\nthe existing files will be ignored.\\n\\n    \\n    \\n    mlflow models update-pip-requirements [OPTIONS] {add|remove}\\n                                          [REQUIREMENT_STRINGS]...\\n    \\n\\nOptions\\n\\n-m, \\\\--model-uri <URI>\\n    \\n\\n**Required** URI to the model.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 40494, \"end_char_idx\": 42610, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/cli.html","doc_id":"https://mlflow.org/docs/latest/cli.html","ref_doc_id":"https://mlflow.org/docs/latest/cli.html"}
{"_node_content":"{\"id_\": \"52fed91b-1a2e-4072-8fad-3d58d316a80b\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/cli.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"2a8ac9b19cbddee491423c178812937f540f6dfd863499d20a2eb0461839b388\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"19f9ab66-b02c-4148-b9e0-bec31d12e39a\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"d4f850719b7f4bc3218a4fddbceb648d757a331e7c50143878779c8ad5a07b10\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"2e140b43-f2c0-457d-bcb8-37e1a1082442\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"fee5656a5114b90d24c50c0bdcfee6427730aa3b23c81b2c340a41423e71d545\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"URIs in the form `models:/`) are not\\nsupported, as artifacts in the model registry are intended to be read-only.\\nEditing requirements is read-only artifact repositories is also not supported.\\n\\nIf adding requirements, the function will overwrite any existing requirements\\nthat overlap, or else append the new requirements to the existing list.\\n\\nIf removing requirements, the function will ignore any version specifiers, and\\nremove all the specified package names. Any requirements that are not found in\\nthe existing files will be ignored.\\n\\n    \\n    \\n    mlflow models update-pip-requirements [OPTIONS] {add|remove}\\n                                          [REQUIREMENT_STRINGS]...\\n    \\n\\nOptions\\n\\n-m, \\\\--model-uri <URI>\\n    \\n\\n**Required** URI to the model. A local path, a \\u00e2\\u0080\\u0098runs:/\\u00e2\\u0080\\u0099 URI, or a remote\\nstorage URI (e.g., an \\u00e2\\u0080\\u0098s3://\\u00e2\\u0080\\u0099 URI). For more information about supported\\nremote URIs for model artifacts, see\\nhttps://mlflow.org/docs/latest/tracking.html#artifact-stores\\n\\nArguments\\n\\nOPERATION\\n\\n    \\n\\nRequired argument\\n\\nREQUIREMENT_STRINGS\\n\\n    \\n\\nOptional argument(s)\\n\\n### run\\n\\nRun an MLflow project from the given URI.\\n\\nFor local runs, the run will block until it completes. Otherwise, the project\\nwill run asynchronously.\\n\\nIf running locally (the default), the URI can be either a Git repository URI\\nor a local path. If running on Databricks, the URI must be a Git repository.\\n\\nBy default, Git projects run in a new working directory with the given\\nparameters, while local projects run from the project\\u00e2\\u0080\\u0099s root directory.\\n\\n    \\n    \\n    mlflow run [OPTIONS] URI\\n    \\n\\nOptions\\n\\n-e, \\\\--entry-point <NAME>\\n    \\n\\nEntry point within project. [default: main]. If the entry point is not found,\\nattempts to run the project file with the specified name as a script, using\\n\\u00e2\\u0080\\u0098python\\u00e2\\u0080\\u0099 to run .py files and the default shell (specified by environment\\nvariable $SHELL) to run .sh files\\n\\n-v, \\\\--version <VERSION>\\n    \\n\\nVersion of the project to run, as a Git commit reference for Git projects.\\n\\n-P, \\\\--param-list <NAME=VALUE>\\n    \\n\\nA parameter for the run, of the form -P name=value.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 41855, \"end_char_idx\": 43934, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/cli.html","doc_id":"https://mlflow.org/docs/latest/cli.html","ref_doc_id":"https://mlflow.org/docs/latest/cli.html"}
{"_node_content":"{\"id_\": \"2e140b43-f2c0-457d-bcb8-37e1a1082442\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/cli.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"2a8ac9b19cbddee491423c178812937f540f6dfd863499d20a2eb0461839b388\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"52fed91b-1a2e-4072-8fad-3d58d316a80b\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"0275815d21bd81a76ca793a7596d44f53cb86fec9e0dbc1eb8bf362f4792a07a\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"4b8f9f13-d634-4e2f-84dd-21c311c9faac\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"19e25f1a735d40823f5bb66771caa6395d2f92fd777e58289b52b7adae56febc\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"If running on Databricks, the URI must be a Git repository.\\n\\nBy default, Git projects run in a new working directory with the given\\nparameters, while local projects run from the project\\u00e2\\u0080\\u0099s root directory.\\n\\n    \\n    \\n    mlflow run [OPTIONS] URI\\n    \\n\\nOptions\\n\\n-e, \\\\--entry-point <NAME>\\n    \\n\\nEntry point within project. [default: main]. If the entry point is not found,\\nattempts to run the project file with the specified name as a script, using\\n\\u00e2\\u0080\\u0098python\\u00e2\\u0080\\u0099 to run .py files and the default shell (specified by environment\\nvariable $SHELL) to run .sh files\\n\\n-v, \\\\--version <VERSION>\\n    \\n\\nVersion of the project to run, as a Git commit reference for Git projects.\\n\\n-P, \\\\--param-list <NAME=VALUE>\\n    \\n\\nA parameter for the run, of the form -P name=value. Provided parameters that\\nare not in the list of parameters for an entry point will be passed to the\\ncorresponding entry point as command-line arguments in the form \\u00e2\\u0080\\u0093name value\\n\\n-A, \\\\--docker-args <NAME=VALUE>\\n    \\n\\nA docker run argument or flag, of the form -A name=value (e.g. -A gpus=all) or\\n-A name (e.g. -A t). The argument will then be passed as docker run \\u00e2\\u0080\\u0093name\\nvalue or docker run \\u00e2\\u0080\\u0093name respectively.\\n\\n\\\\--experiment-name <experiment_name>\\n\\n    \\n\\nName of the experiment under which to launch the run. If not specified,\\n\\u00e2\\u0080\\u0098experiment-id\\u00e2\\u0080\\u0099 option will be used to launch run.\\n\\n\\\\--experiment-id <experiment_id>\\n\\n    \\n\\nID of the experiment under which to launch the run.\\n\\n-b, \\\\--backend <BACKEND>\\n    \\n\\nExecution backend to use for run. Supported values: \\u00e2\\u0080\\u0098local\\u00e2\\u0080\\u0099,\\n\\u00e2\\u0080\\u0098databricks\\u00e2\\u0080\\u0099, kubernetes (experimental). Defaults to \\u00e2\\u0080\\u0098local\\u00e2\\u0080\\u0099. If\\nrunning against Databricks, will run against a Databricks workspace determined\\nas follows: if a Databricks tracking URI of the form\\n\\u00e2\\u0080\\u0098databricks://profile\\u00e2\\u0080\\u0099 has been set (e.g. by setting the\\nMLFLOW_TRACKING_URI environment variable), will run against the workspace\\nspecified by <profile>. Otherwise, runs against the workspace specified by the\\ndefault Databricks CLI profile.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 43179, \"end_char_idx\": 45161, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/cli.html","doc_id":"https://mlflow.org/docs/latest/cli.html","ref_doc_id":"https://mlflow.org/docs/latest/cli.html"}
{"_node_content":"{\"id_\": \"4b8f9f13-d634-4e2f-84dd-21c311c9faac\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/cli.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"2a8ac9b19cbddee491423c178812937f540f6dfd863499d20a2eb0461839b388\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"2e140b43-f2c0-457d-bcb8-37e1a1082442\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"fee5656a5114b90d24c50c0bdcfee6427730aa3b23c81b2c340a41423e71d545\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"396d66d8-1c92-4dbf-8133-f1da99b264fd\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"dde953e7af7cf2073f52f4ebb517f3aa512ef96de671c8c2559723a72f172b18\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"If not specified,\\n\\u00e2\\u0080\\u0098experiment-id\\u00e2\\u0080\\u0099 option will be used to launch run.\\n\\n\\\\--experiment-id <experiment_id>\\n\\n    \\n\\nID of the experiment under which to launch the run.\\n\\n-b, \\\\--backend <BACKEND>\\n    \\n\\nExecution backend to use for run. Supported values: \\u00e2\\u0080\\u0098local\\u00e2\\u0080\\u0099,\\n\\u00e2\\u0080\\u0098databricks\\u00e2\\u0080\\u0099, kubernetes (experimental). Defaults to \\u00e2\\u0080\\u0098local\\u00e2\\u0080\\u0099. If\\nrunning against Databricks, will run against a Databricks workspace determined\\nas follows: if a Databricks tracking URI of the form\\n\\u00e2\\u0080\\u0098databricks://profile\\u00e2\\u0080\\u0099 has been set (e.g. by setting the\\nMLFLOW_TRACKING_URI environment variable), will run against the workspace\\nspecified by <profile>. Otherwise, runs against the workspace specified by the\\ndefault Databricks CLI profile. See https://github.com/databricks/databricks-\\ncli for more info on configuring a Databricks CLI profile.\\n\\n-c, \\\\--backend-config <FILE>\\n    \\n\\nPath to JSON file (must end in \\u00e2\\u0080\\u0098.json\\u00e2\\u0080\\u0099) or JSON string which will be\\npassed as config to the backend. The exact content which should be provided is\\ndifferent for each execution backend and is documented at\\n<https://www.mlflow.org/docs/latest/projects.html>.\\n\\n\\\\--env-manager <env_manager>\\n\\n    \\n\\nIf specified, create an environment for MLproject using the specified\\nenvironment manager. The following values are supported:\\n\\n\\\\- local: use the local environment\\n\\n\\\\- virtualenv: use virtualenv (and pyenv for Python version management)\\n\\n\\\\- conda: use conda\\n\\nIf unspecified, the appropriate environment manager is automatically selected\\nbased on the project configuration. For example, if MLproject.yaml contains a\\npython_env key, virtualenv is used.\\n\\n\\\\--storage-dir <storage_dir>\\n\\n    \\n\\nOnly valid when `backend` is local. MLflow downloads artifacts from\\ndistributed URIs passed to parameters of type \\u00e2\\u0080\\u0098path\\u00e2\\u0080\\u0099 to subdirectories of\\nstorage_dir.\\n\\n\\\\--run-id <RUN_ID>\\n\\n    \\n\\nIf specified, the given run ID will be used instead of creating a new run.\\nNote: this argument is used internally by the MLflow project APIs and should\\nnot be specified.\\n\\n\\\\--run-name <RUN_NAME>\\n\\n    \\n\\nThe name to give the MLflow Run associated with the project execution.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 44448, \"end_char_idx\": 46547, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/cli.html","doc_id":"https://mlflow.org/docs/latest/cli.html","ref_doc_id":"https://mlflow.org/docs/latest/cli.html"}
{"_node_content":"{\"id_\": \"396d66d8-1c92-4dbf-8133-f1da99b264fd\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/cli.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"2a8ac9b19cbddee491423c178812937f540f6dfd863499d20a2eb0461839b388\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"4b8f9f13-d634-4e2f-84dd-21c311c9faac\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"19e25f1a735d40823f5bb66771caa6395d2f92fd777e58289b52b7adae56febc\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"41e02aa0-d4a4-44f3-9e8e-f099b2761259\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"dbf7b0ccf7545e14fcd65de0c5eb8daba50923f7e32e1ff7907af33790542b52\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"The following values are supported:\\n\\n\\\\- local: use the local environment\\n\\n\\\\- virtualenv: use virtualenv (and pyenv for Python version management)\\n\\n\\\\- conda: use conda\\n\\nIf unspecified, the appropriate environment manager is automatically selected\\nbased on the project configuration. For example, if MLproject.yaml contains a\\npython_env key, virtualenv is used.\\n\\n\\\\--storage-dir <storage_dir>\\n\\n    \\n\\nOnly valid when `backend` is local. MLflow downloads artifacts from\\ndistributed URIs passed to parameters of type \\u00e2\\u0080\\u0098path\\u00e2\\u0080\\u0099 to subdirectories of\\nstorage_dir.\\n\\n\\\\--run-id <RUN_ID>\\n\\n    \\n\\nIf specified, the given run ID will be used instead of creating a new run.\\nNote: this argument is used internally by the MLflow project APIs and should\\nnot be specified.\\n\\n\\\\--run-name <RUN_NAME>\\n\\n    \\n\\nThe name to give the MLflow Run associated with the project execution. If not\\nspecified, the MLflow Run name is left unset.\\n\\n\\\\--build-image\\n\\n    \\n\\nOnly valid for Docker projects. If specified, build a new Docker image\\nthat\\u00e2\\u0080\\u0099s based on the image specified by the image field in the MLproject\\nfile, and contains files in the project directory.\\n\\nDefault\\n\\n    \\n\\n`False`\\n\\nArguments\\n\\nURI\\n\\n    \\n\\nRequired argument\\n\\nEnvironment variables\\n\\nMLFLOW_EXPERIMENT_NAME\\n\\n    \\n\\n> Provide a default for `--experiment-name`\\n\\nMLFLOW_EXPERIMENT_ID\\n\\n    \\n\\n> Provide a default for `--experiment-id`\\n\\nMLFLOW_TMP_DIR\\n\\n    \\n\\n> Provide a default for `--storage-dir`\\n\\n### runs\\n\\nManage runs. To manage runs of experiments associated with a tracking server,\\nset the MLFLOW_TRACKING_URI environment variable to the URL of the desired\\nserver.\\n\\n    \\n    \\n    mlflow runs [OPTIONS] COMMAND [ARGS]...\\n    \\n\\n#### delete\\n\\nMark a run for deletion. Return an error if the run does not exist or is\\nalready marked. You can restore a marked run with `restore_run`, or\\npermanently delete a run in the backend store.\\n\\n    \\n    \\n    mlflow runs delete [OPTIONS]\\n    \\n\\nOptions\\n\\n\\\\--run-id <run_id>\\n\\n    \\n\\n**Required**\\n\\n#### describe\\n\\nAll of run details will print to the stdout as JSON format.\\n\\n    \\n    \\n    mlflow runs describe [OPTIONS]\\n    \\n\\nOptions\\n\\n\\\\--run-id <run_id>\\n\\n    \\n\\n**Required**\\n\\n#### list\\n\\nList all runs of the specified experiment in the configured tracking server.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 45693, \"end_char_idx\": 47913, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/cli.html","doc_id":"https://mlflow.org/docs/latest/cli.html","ref_doc_id":"https://mlflow.org/docs/latest/cli.html"}
{"_node_content":"{\"id_\": \"41e02aa0-d4a4-44f3-9e8e-f099b2761259\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/cli.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"2a8ac9b19cbddee491423c178812937f540f6dfd863499d20a2eb0461839b388\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"396d66d8-1c92-4dbf-8133-f1da99b264fd\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"dde953e7af7cf2073f52f4ebb517f3aa512ef96de671c8c2559723a72f172b18\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"72648a31-f9e9-47b5-8123-d7c1637f997a\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"4a1b0df668f502c2288d70173998966c3830fa4105853ee5f523b53c5d365a23\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"To manage runs of experiments associated with a tracking server,\\nset the MLFLOW_TRACKING_URI environment variable to the URL of the desired\\nserver.\\n\\n    \\n    \\n    mlflow runs [OPTIONS] COMMAND [ARGS]...\\n    \\n\\n#### delete\\n\\nMark a run for deletion. Return an error if the run does not exist or is\\nalready marked. You can restore a marked run with `restore_run`, or\\npermanently delete a run in the backend store.\\n\\n    \\n    \\n    mlflow runs delete [OPTIONS]\\n    \\n\\nOptions\\n\\n\\\\--run-id <run_id>\\n\\n    \\n\\n**Required**\\n\\n#### describe\\n\\nAll of run details will print to the stdout as JSON format.\\n\\n    \\n    \\n    mlflow runs describe [OPTIONS]\\n    \\n\\nOptions\\n\\n\\\\--run-id <run_id>\\n\\n    \\n\\n**Required**\\n\\n#### list\\n\\nList all runs of the specified experiment in the configured tracking server.\\n\\n    \\n    \\n    mlflow runs list [OPTIONS]\\n    \\n\\nOptions\\n\\n\\\\--experiment-id <experiment_id>\\n\\n    \\n\\n**Required** Specify the experiment ID for list of runs.\\n\\n-v, \\\\--view <view>\\n    \\n\\nSelect view type for list experiments. Valid view types are \\u00e2\\u0080\\u0098active_only\\u00e2\\u0080\\u0099\\n(default), \\u00e2\\u0080\\u0098deleted_only\\u00e2\\u0080\\u0099, and \\u00e2\\u0080\\u0098all\\u00e2\\u0080\\u0099.\\n\\nEnvironment variables\\n\\nMLFLOW_EXPERIMENT_ID\\n\\n    \\n\\n> Provide a default for `--experiment-id`\\n\\n#### restore\\n\\nRestore a deleted run. Returns an error if the run is active or has been\\npermanently deleted.\\n\\n    \\n    \\n    mlflow runs restore [OPTIONS]\\n    \\n\\nOptions\\n\\n\\\\--run-id <run_id>\\n\\n    \\n\\n**Required**\\n\\n### sagemaker\\n\\nServe models on SageMaker.\\n\\nTo serve a model associated with a run on a tracking server, set the\\nMLFLOW_TRACKING_URI environment variable to the URL of the desired server.\\n\\n    \\n    \\n    mlflow sagemaker [OPTIONS] COMMAND [ARGS]...\\n    \\n\\n#### build-and-push-container\\n\\nBuild new MLflow Sagemaker image, assign it a name, and push to ECR.\\n\\nThis function builds an MLflow Docker image. The image is built locally and it\\nrequires Docker to run. The image is pushed to ECR under current active AWS\\naccount and to current active AWS region.\\n\\n    \\n    \\n    mlflow sagemaker build-and-push-container [OPTIONS]\\n    \\n\\nOptions\\n\\n\\\\--build, \\\\--no-build\\n\\n    \\n\\nBuild the container if set.\\n\\n\\\\--push, \\\\--no-push\\n\\n    \\n\\nPush the container to AWS ECR if set.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 47141, \"end_char_idx\": 49276, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/cli.html","doc_id":"https://mlflow.org/docs/latest/cli.html","ref_doc_id":"https://mlflow.org/docs/latest/cli.html"}
{"_node_content":"{\"id_\": \"72648a31-f9e9-47b5-8123-d7c1637f997a\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/cli.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"2a8ac9b19cbddee491423c178812937f540f6dfd863499d20a2eb0461839b388\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"41e02aa0-d4a4-44f3-9e8e-f099b2761259\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"dbf7b0ccf7545e14fcd65de0c5eb8daba50923f7e32e1ff7907af33790542b52\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"98a91209-2b8a-4c99-90e1-a3c8590a949f\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"1f89e3a211212bdd722d08dc68b69cfb089c26aa691cc150e095464db304cc97\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"To serve a model associated with a run on a tracking server, set the\\nMLFLOW_TRACKING_URI environment variable to the URL of the desired server.\\n\\n    \\n    \\n    mlflow sagemaker [OPTIONS] COMMAND [ARGS]...\\n    \\n\\n#### build-and-push-container\\n\\nBuild new MLflow Sagemaker image, assign it a name, and push to ECR.\\n\\nThis function builds an MLflow Docker image. The image is built locally and it\\nrequires Docker to run. The image is pushed to ECR under current active AWS\\naccount and to current active AWS region.\\n\\n    \\n    \\n    mlflow sagemaker build-and-push-container [OPTIONS]\\n    \\n\\nOptions\\n\\n\\\\--build, \\\\--no-build\\n\\n    \\n\\nBuild the container if set.\\n\\n\\\\--push, \\\\--no-push\\n\\n    \\n\\nPush the container to AWS ECR if set.\\n\\n-c, \\\\--container <container>\\n    \\n\\nimage name\\n\\n\\\\--install-java <install_java>\\n\\n    \\n\\nInstalls Java in the image if needed. Default is None, allowing MLflow to\\ndetermine installation. Flavors requiring Java, such as Spark, enable this\\nautomatically. Note: This option only works with the UBUNTU base image; Python\\nbase images do not support Java installation.\\n\\n\\\\--env-manager <env_manager>\\n\\n    \\n\\nIf specified, create an environment for MLmodel using the specified\\nenvironment manager. The following values are supported:\\n\\n\\\\- local: use the local environment\\n\\n\\\\- virtualenv: use virtualenv (and pyenv for Python version management)\\n\\n\\\\- conda: use conda\\n\\nIf unspecified, default to virtualenv.\\n\\n\\\\--mlflow-home <PATH>\\n\\n    \\n\\nPath to local clone of MLflow project. Use for development only.\\n\\n#### deploy-transform-job\\n\\nDeploy model on Sagemaker as a batch transform job. Current active AWS account\\nneeds to have correct permissions setup.\\n\\nBy default, unless the `--async` flag is specified, this command will block\\nuntil either the batch transform job completes (definitively succeeds or\\nfails) or the specified timeout elapses.\\n\\n    \\n    \\n    mlflow sagemaker deploy-transform-job [OPTIONS]\\n    \\n\\nOptions\\n\\n-n, \\\\--job-name <job_name>\\n    \\n\\n**Required** Transform job name\\n\\n-m, \\\\--model-uri <URI>\\n    \\n\\n**Required** URI to the model. A local path, a \\u00e2\\u0080\\u0098runs:/\\u00e2\\u0080\\u0099 URI, or a remote\\nstorage URI (e.g., an \\u00e2\\u0080\\u0098s3://\\u00e2\\u0080\\u0099 URI).\", \"mimetype\": \"text/plain\", \"start_char_idx\": 48564, \"end_char_idx\": 50693, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/cli.html","doc_id":"https://mlflow.org/docs/latest/cli.html","ref_doc_id":"https://mlflow.org/docs/latest/cli.html"}
{"_node_content":"{\"id_\": \"98a91209-2b8a-4c99-90e1-a3c8590a949f\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/cli.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"2a8ac9b19cbddee491423c178812937f540f6dfd863499d20a2eb0461839b388\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"72648a31-f9e9-47b5-8123-d7c1637f997a\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"4a1b0df668f502c2288d70173998966c3830fa4105853ee5f523b53c5d365a23\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"8f762822-e4d2-4b6a-b37b-579d9cd078ea\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"7bca8aac935d4b43d2348fd9272164964b5912cf8ec83bcc26cf1e3908baecd5\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"\\\\--mlflow-home <PATH>\\n\\n    \\n\\nPath to local clone of MLflow project. Use for development only.\\n\\n#### deploy-transform-job\\n\\nDeploy model on Sagemaker as a batch transform job. Current active AWS account\\nneeds to have correct permissions setup.\\n\\nBy default, unless the `--async` flag is specified, this command will block\\nuntil either the batch transform job completes (definitively succeeds or\\nfails) or the specified timeout elapses.\\n\\n    \\n    \\n    mlflow sagemaker deploy-transform-job [OPTIONS]\\n    \\n\\nOptions\\n\\n-n, \\\\--job-name <job_name>\\n    \\n\\n**Required** Transform job name\\n\\n-m, \\\\--model-uri <URI>\\n    \\n\\n**Required** URI to the model. A local path, a \\u00e2\\u0080\\u0098runs:/\\u00e2\\u0080\\u0099 URI, or a remote\\nstorage URI (e.g., an \\u00e2\\u0080\\u0098s3://\\u00e2\\u0080\\u0099 URI). For more information about supported\\nremote URIs for model artifacts, see\\nhttps://mlflow.org/docs/latest/tracking.html#artifact-stores\\n\\n\\\\--input-data-type <input_data_type>\\n\\n    \\n\\n**Required** Input data type for the transform job\\n\\n-u, \\\\--input-uri <input_uri>\\n    \\n\\n**Required** S3 key name prefix or manifest of the input data\\n\\n\\\\--content-type <content_type>\\n\\n    \\n\\n**Required** The multipurpose internet mail extension (MIME) type of the data\\n\\n-o, \\\\--output-path <output_path>\\n    \\n\\n**Required** The S3 path to store the output results of the Sagemaker\\ntransform job\\n\\n\\\\--compression-type <compression_type>\\n\\n    \\n\\nThe compression type of the transform data\\n\\n-s, \\\\--split-type <split_type>\\n    \\n\\nThe method to split the transform job\\u00e2\\u0080\\u0099s data files into smaller batches\\n\\n-a, \\\\--accept <accept>\\n    \\n\\nThe multipurpose internet mail extension (MIME) type of the output data\\n\\n\\\\--assemble-with <assemble_with>\\n\\n    \\n\\nThe method to assemble the results of the transform job as a single S3 object\\n\\n\\\\--input-filter <input_filter>\\n\\n    \\n\\nA JSONPath expression used to select a portion of the input data for the\\ntransform job\\n\\n\\\\--output-filter <output_filter>\\n\\n    \\n\\nA JSONPath expression used to select a portion of the output data from the\\ntransform job\\n\\n-j, \\\\--join-resource <join_resource>\\n    \\n\\nThe source of the data to join with the transformed data\\n\\n-e, \\\\--execution-role-arn <execution_role_arn>\\n    \\n\\nSageMaker execution role\\n\\n-b, \\\\--bucket <bucket>\\n    \\n\\nS3 bucket to store model artifacts\\n\\n-i, \\\\--image-url <image_url>\\n    \\n\\nECR URL for the Docker image\\n\\n\\\\--region-name <region_name>\\n\\n    \\n\\nName of the AWS region in which to deploy the transform job\\n\\n-t, \\\\--instance-type <instance_type>\\n    \\n\\nThe type of SageMaker ML instance on which to perform the batch transform job.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 49971, \"end_char_idx\": 52472, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/cli.html","doc_id":"https://mlflow.org/docs/latest/cli.html","ref_doc_id":"https://mlflow.org/docs/latest/cli.html"}
{"_node_content":"{\"id_\": \"8f762822-e4d2-4b6a-b37b-579d9cd078ea\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/cli.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"2a8ac9b19cbddee491423c178812937f540f6dfd863499d20a2eb0461839b388\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"98a91209-2b8a-4c99-90e1-a3c8590a949f\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"1f89e3a211212bdd722d08dc68b69cfb089c26aa691cc150e095464db304cc97\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"77e139b5-d2be-4cf2-b49a-35eae806f5e0\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"58f227e31ae10b885625be48e6ff065657e5f0e713e59794a7bf9fce0190ffe1\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"For a list of supported instance types, see\\n<https://aws.amazon.com/sagemaker/pricing/instance-types/>.\\n\\n-c, \\\\--instance-count <instance_count>\\n    \\n\\nThe number of SageMaker ML instances on which to perform the batch transform\\njob\\n\\n-v, \\\\--vpc-config <vpc_config>\\n    \\n\\nPath to a file containing a JSON-formatted VPC configuration. This\\nconfiguration will be used when creating the new SageMaker model associated\\nwith this application. For more information, see\\n<https://docs.aws.amazon.com/sagemaker/latest/dg/API_VpcConfig.html>\\n\\n-f, \\\\--flavor <flavor>\\n    \\n\\nThe name of the flavor to use for deployment. Must be one of the following:\\n[\\u00e2\\u0080\\u0098python_function\\u00e2\\u0080\\u0099]. If unspecified, a flavor will be automatically\\nselected from the model\\u00e2\\u0080\\u0099s available flavors.\\n\\n\\\\--archive\\n\\n    \\n\\nIf specified, any SageMaker resources that become inactive after the finished\\nbatch transform job are preserved. These resources may include the associated\\nSageMaker models and model artifacts. Otherwise, if \\u00e2\\u0080\\u0093archive is unspecified,\\nthese resources are deleted. \\u00e2\\u0080\\u0093archive must be specified when deploying\\nasynchronously with \\u00e2\\u0080\\u0093async.\\n\\n\\\\--async\\n\\n    \\n\\nIf specified, this command will return immediately after starting the\\ndeployment process. It will not wait for the deployment process to complete.\\nThe caller is responsible for monitoring the deployment process via native\\nSageMaker APIs or the AWS console.\\n\\n\\\\--timeout <timeout>\\n\\n    \\n\\nIf the command is executed synchronously, the deployment process will return\\nafter the specified number of seconds if no definitive result (success or\\nfailure) is achieved. Once the function returns, the caller is responsible for\\nmonitoring the health and status of the pending deployment via native\\nSageMaker APIs or the AWS console. If the command is executed asynchronously\\nusing the \\u00e2\\u0080\\u0093async flag, this value is ignored.\\n\\n#### push-model\\n\\nPush an MLflow model to Sagemaker model registry. Current active AWS account\\nneeds to have correct permissions setup.\\n\\n    \\n    \\n    mlflow sagemaker push-model [OPTIONS]\\n    \\n\\nOptions\\n\\n-n, \\\\--model-name <model_name>\\n    \\n\\n**Required** Sagemaker model name\\n\\n-m, \\\\--model-uri <URI>\\n    \\n\\n**Required** URI to the model.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 52473, \"end_char_idx\": 54648, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/cli.html","doc_id":"https://mlflow.org/docs/latest/cli.html","ref_doc_id":"https://mlflow.org/docs/latest/cli.html"}
{"_node_content":"{\"id_\": \"77e139b5-d2be-4cf2-b49a-35eae806f5e0\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/cli.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"2a8ac9b19cbddee491423c178812937f540f6dfd863499d20a2eb0461839b388\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"8f762822-e4d2-4b6a-b37b-579d9cd078ea\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"7bca8aac935d4b43d2348fd9272164964b5912cf8ec83bcc26cf1e3908baecd5\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"d673d21e-3978-44ed-abba-41062531eb1b\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"0e4372e8c17c3d04c0ce88cb5914375be1f39976bb2ba26656bb974cbd66e919\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"The caller is responsible for monitoring the deployment process via native\\nSageMaker APIs or the AWS console.\\n\\n\\\\--timeout <timeout>\\n\\n    \\n\\nIf the command is executed synchronously, the deployment process will return\\nafter the specified number of seconds if no definitive result (success or\\nfailure) is achieved. Once the function returns, the caller is responsible for\\nmonitoring the health and status of the pending deployment via native\\nSageMaker APIs or the AWS console. If the command is executed asynchronously\\nusing the \\u00e2\\u0080\\u0093async flag, this value is ignored.\\n\\n#### push-model\\n\\nPush an MLflow model to Sagemaker model registry. Current active AWS account\\nneeds to have correct permissions setup.\\n\\n    \\n    \\n    mlflow sagemaker push-model [OPTIONS]\\n    \\n\\nOptions\\n\\n-n, \\\\--model-name <model_name>\\n    \\n\\n**Required** Sagemaker model name\\n\\n-m, \\\\--model-uri <URI>\\n    \\n\\n**Required** URI to the model. A local path, a \\u00e2\\u0080\\u0098runs:/\\u00e2\\u0080\\u0099 URI, or a remote\\nstorage URI (e.g., an \\u00e2\\u0080\\u0098s3://\\u00e2\\u0080\\u0099 URI). For more information about supported\\nremote URIs for model artifacts, see\\nhttps://mlflow.org/docs/latest/tracking.html#artifact-stores\\n\\n-e, \\\\--execution-role-arn <execution_role_arn>\\n    \\n\\nSageMaker execution role\\n\\n-b, \\\\--bucket <bucket>\\n    \\n\\nS3 bucket to store model artifacts\\n\\n-i, \\\\--image-url <image_url>\\n    \\n\\nECR URL for the Docker image\\n\\n\\\\--region-name <region_name>\\n\\n    \\n\\nName of the AWS region in which to push the Sagemaker model\\n\\n-v, \\\\--vpc-config <vpc_config>\\n    \\n\\nPath to a file containing a JSON-formatted VPC configuration. This\\nconfiguration will be used when creating the new SageMaker model. For more\\ninformation, see\\n<https://docs.aws.amazon.com/sagemaker/latest/dg/API_VpcConfig.html>\\n\\n-f, \\\\--flavor <flavor>\\n    \\n\\nThe name of the flavor to use for deployment. Must be one of the following:\\n[\\u00e2\\u0080\\u0098python_function\\u00e2\\u0080\\u0099]. If unspecified, a flavor will be automatically\\nselected from the model\\u00e2\\u0080\\u0099s available flavors.\\n\\n#### terminate-transform-job\\n\\nTerminate the specified Sagemaker batch transform job.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 53749, \"end_char_idx\": 55752, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/cli.html","doc_id":"https://mlflow.org/docs/latest/cli.html","ref_doc_id":"https://mlflow.org/docs/latest/cli.html"}
{"_node_content":"{\"id_\": \"d673d21e-3978-44ed-abba-41062531eb1b\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/cli.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"2a8ac9b19cbddee491423c178812937f540f6dfd863499d20a2eb0461839b388\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"77e139b5-d2be-4cf2-b49a-35eae806f5e0\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"58f227e31ae10b885625be48e6ff065657e5f0e713e59794a7bf9fce0190ffe1\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"da19fb02-9764-4e52-978c-81ef9a892471\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"842755636a0e31563c6f9b3576805f0f4480cd6f3de9462a4ed0e83692e912eb\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"This\\nconfiguration will be used when creating the new SageMaker model. For more\\ninformation, see\\n<https://docs.aws.amazon.com/sagemaker/latest/dg/API_VpcConfig.html>\\n\\n-f, \\\\--flavor <flavor>\\n    \\n\\nThe name of the flavor to use for deployment. Must be one of the following:\\n[\\u00e2\\u0080\\u0098python_function\\u00e2\\u0080\\u0099]. If unspecified, a flavor will be automatically\\nselected from the model\\u00e2\\u0080\\u0099s available flavors.\\n\\n#### terminate-transform-job\\n\\nTerminate the specified Sagemaker batch transform job. Unless `--archive` is\\nspecified, all SageMaker resources associated with the batch transform job are\\ndeleted as well.\\n\\nBy default, unless the `--async` flag is specified, this command will block\\nuntil either the termination process completes (definitively succeeds or\\nfails) or the specified timeout elapses.\\n\\n    \\n    \\n    mlflow sagemaker terminate-transform-job [OPTIONS]\\n    \\n\\nOptions\\n\\n-n, \\\\--job-name <job_name>\\n    \\n\\n**Required** Transform job name\\n\\n-r, \\\\--region-name <region_name>\\n    \\n\\nName of the AWS region in which the transform job is deployed\\n\\n\\\\--archive\\n\\n    \\n\\nIf specified, resources associated with the application are preserved. These\\nresources may include unused SageMaker models and model artifacts. Otherwise,\\nif \\u00e2\\u0080\\u0093archive is unspecified, these resources are deleted. \\u00e2\\u0080\\u0093archive must be\\nspecified when deleting asynchronously with \\u00e2\\u0080\\u0093async.\\n\\n\\\\--async\\n\\n    \\n\\nIf specified, this command will return immediately after starting the\\ntermination process. It will not wait for the termination process to complete.\\nThe caller is responsible for monitoring the termination process via native\\nSageMaker APIs or the AWS console.\\n\\n\\\\--timeout <timeout>\\n\\n    \\n\\nIf the command is executed synchronously, the termination process will return\\nafter the specified number of seconds if no definitive result (success or\\nfailure) is achieved. Once the function returns, the caller is responsible for\\nmonitoring the health and status of the pending termination via native\\nSageMaker APIs or the AWS console. If the command is executed asynchronously\\nusing the \\u00e2\\u0080\\u0093async flag, this value is ignored.\\n\\n### server\\n\\nRun the MLflow tracking server.\\n\\nThe server listens on <http://localhost:5000> by default and only accepts\\nconnections from the local machine.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 55276, \"end_char_idx\": 57504, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/cli.html","doc_id":"https://mlflow.org/docs/latest/cli.html","ref_doc_id":"https://mlflow.org/docs/latest/cli.html"}
{"_node_content":"{\"id_\": \"da19fb02-9764-4e52-978c-81ef9a892471\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/cli.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"2a8ac9b19cbddee491423c178812937f540f6dfd863499d20a2eb0461839b388\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"d673d21e-3978-44ed-abba-41062531eb1b\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"0e4372e8c17c3d04c0ce88cb5914375be1f39976bb2ba26656bb974cbd66e919\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"431dbdda-7b61-4ff2-93ea-adbcf3432748\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"2f7fcf5b542e5e71f1aaca5173e08168296fa960818582db4bd3fbd9cb28a09b\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"\\u00e2\\u0080\\u0093archive must be\\nspecified when deleting asynchronously with \\u00e2\\u0080\\u0093async.\\n\\n\\\\--async\\n\\n    \\n\\nIf specified, this command will return immediately after starting the\\ntermination process. It will not wait for the termination process to complete.\\nThe caller is responsible for monitoring the termination process via native\\nSageMaker APIs or the AWS console.\\n\\n\\\\--timeout <timeout>\\n\\n    \\n\\nIf the command is executed synchronously, the termination process will return\\nafter the specified number of seconds if no definitive result (success or\\nfailure) is achieved. Once the function returns, the caller is responsible for\\nmonitoring the health and status of the pending termination via native\\nSageMaker APIs or the AWS console. If the command is executed asynchronously\\nusing the \\u00e2\\u0080\\u0093async flag, this value is ignored.\\n\\n### server\\n\\nRun the MLflow tracking server.\\n\\nThe server listens on <http://localhost:5000> by default and only accepts\\nconnections from the local machine. To let the server accept connections from\\nother machines, you will need to pass `--host 0.0.0.0` to listen on all\\nnetwork interfaces (or a specific interface address).\\n\\n    \\n    \\n    mlflow server [OPTIONS]\\n    \\n\\nOptions\\n\\n\\\\--backend-store-uri <PATH>\\n\\n    \\n\\nURI to which to persist experiment and run data. Acceptable URIs are\\nSQLAlchemy-compatible database connection strings (e.g.\\n\\u00e2\\u0080\\u0098sqlite:///path/to/file.db\\u00e2\\u0080\\u0099) or local filesystem URIs (e.g.\\n\\u00e2\\u0080\\u0098file:///absolute/path/to/directory\\u00e2\\u0080\\u0099). By default, data will be logged to\\nthe ./mlruns directory.\\n\\n\\\\--registry-store-uri <URI>\\n\\n    \\n\\nURI to which to persist registered models. Acceptable URIs are SQLAlchemy-\\ncompatible database connection strings (e.g. \\u00e2\\u0080\\u0098sqlite:///path/to/file.db\\u00e2\\u0080\\u0099).\\nIf not specified, backend-store-uri is used.\\n\\n\\\\--default-artifact-root <URI>\\n\\n    \\n\\nDirectory in which to store artifacts for any new experiments created. For\\ntracking server backends that rely on SQL, this option is required in order to\\nstore artifacts. Note that this flag does not impact already-created\\nexperiments with any previous configuration of an MLflow server instance. By\\ndefault, data will be logged to the mlflow-artifacts:/ uri proxy if the\\n\\u00e2\\u0080\\u0093serve-artifacts option is enabled. Otherwise, the default location will be\\n./mlruns.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 56543, \"end_char_idx\": 58786, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/cli.html","doc_id":"https://mlflow.org/docs/latest/cli.html","ref_doc_id":"https://mlflow.org/docs/latest/cli.html"}
{"_node_content":"{\"id_\": \"431dbdda-7b61-4ff2-93ea-adbcf3432748\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/cli.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"2a8ac9b19cbddee491423c178812937f540f6dfd863499d20a2eb0461839b388\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"da19fb02-9764-4e52-978c-81ef9a892471\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"842755636a0e31563c6f9b3576805f0f4480cd6f3de9462a4ed0e83692e912eb\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"0122d3db-98a7-49ca-b8f1-6bcf4cbfbb32\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"311ef81753e5f78b386c6c465c84c7c4414dc7cc0639e5b5f7fbdb3bf7327f83\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"\\u00e2\\u0080\\u0098file:///absolute/path/to/directory\\u00e2\\u0080\\u0099). By default, data will be logged to\\nthe ./mlruns directory.\\n\\n\\\\--registry-store-uri <URI>\\n\\n    \\n\\nURI to which to persist registered models. Acceptable URIs are SQLAlchemy-\\ncompatible database connection strings (e.g. \\u00e2\\u0080\\u0098sqlite:///path/to/file.db\\u00e2\\u0080\\u0099).\\nIf not specified, backend-store-uri is used.\\n\\n\\\\--default-artifact-root <URI>\\n\\n    \\n\\nDirectory in which to store artifacts for any new experiments created. For\\ntracking server backends that rely on SQL, this option is required in order to\\nstore artifacts. Note that this flag does not impact already-created\\nexperiments with any previous configuration of an MLflow server instance. By\\ndefault, data will be logged to the mlflow-artifacts:/ uri proxy if the\\n\\u00e2\\u0080\\u0093serve-artifacts option is enabled. Otherwise, the default location will be\\n./mlruns.\\n\\n\\\\--serve-artifacts, \\\\--no-serve-artifacts\\n\\n    \\n\\nEnables serving of artifact uploads, downloads, and list requests by routing\\nthese requests to the storage location that is specified by \\u00e2\\u0080\\u0098\\u00e2\\u0080\\u0093artifacts-\\ndestination\\u00e2\\u0080\\u0099 directly through a proxy. The default location that these\\nrequests are served from is a local \\u00e2\\u0080\\u0098./mlartifacts\\u00e2\\u0080\\u0099 directory which can be\\noverridden via the \\u00e2\\u0080\\u0098\\u00e2\\u0080\\u0093artifacts-destination\\u00e2\\u0080\\u0099 argument. To disable\\nartifact serving, specify \\u00e2\\u0080\\u0093no-serve-artifacts. Default: True\\n\\n\\\\--artifacts-only\\n\\n    \\n\\nIf specified, configures the mlflow server to be used only for proxied\\nartifact serving. With this mode enabled, functionality of the mlflow tracking\\nservice (e.g. run creation, metric logging, and parameter logging) is\\ndisabled. The server will only expose endpoints for uploading, downloading,\\nand listing artifacts. Default: False\\n\\n\\\\--artifacts-destination <URI>\\n\\n    \\n\\nThe base artifact location from which to resolve artifact upload/download/list\\nrequests (e.g. \\u00e2\\u0080\\u0098s3://my-bucket\\u00e2\\u0080\\u0099). Defaults to a local \\u00e2\\u0080\\u0098./mlartifacts\\u00e2\\u0080\\u0099\\ndirectory. This option only applies when the tracking server is configured to\\nstream artifacts and the experiment\\u00e2\\u0080\\u0099s artifact root location is http or\\nmlflow-artifacts URI.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 57951, \"end_char_idx\": 60006, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/cli.html","doc_id":"https://mlflow.org/docs/latest/cli.html","ref_doc_id":"https://mlflow.org/docs/latest/cli.html"}
{"_node_content":"{\"id_\": \"0122d3db-98a7-49ca-b8f1-6bcf4cbfbb32\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/cli.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"2a8ac9b19cbddee491423c178812937f540f6dfd863499d20a2eb0461839b388\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"431dbdda-7b61-4ff2-93ea-adbcf3432748\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"2f7fcf5b542e5e71f1aaca5173e08168296fa960818582db4bd3fbd9cb28a09b\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"8dd9034c-232c-4b4f-82c0-ef0b7a093d9e\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"b1294b6c1b4e4302adf2d87328908f19e3eeba161721b4ddd1878c0555c94715\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"To disable\\nartifact serving, specify \\u00e2\\u0080\\u0093no-serve-artifacts. Default: True\\n\\n\\\\--artifacts-only\\n\\n    \\n\\nIf specified, configures the mlflow server to be used only for proxied\\nartifact serving. With this mode enabled, functionality of the mlflow tracking\\nservice (e.g. run creation, metric logging, and parameter logging) is\\ndisabled. The server will only expose endpoints for uploading, downloading,\\nand listing artifacts. Default: False\\n\\n\\\\--artifacts-destination <URI>\\n\\n    \\n\\nThe base artifact location from which to resolve artifact upload/download/list\\nrequests (e.g. \\u00e2\\u0080\\u0098s3://my-bucket\\u00e2\\u0080\\u0099). Defaults to a local \\u00e2\\u0080\\u0098./mlartifacts\\u00e2\\u0080\\u0099\\ndirectory. This option only applies when the tracking server is configured to\\nstream artifacts and the experiment\\u00e2\\u0080\\u0099s artifact root location is http or\\nmlflow-artifacts URI.\\n\\n-h, \\\\--host <HOST>\\n    \\n\\nThe network address to listen on (default: 127.0.0.1). Use 0.0.0.0 to bind to\\nall addresses if you want to access the tracking server from other machines.\\n\\n-p, \\\\--port <port>\\n    \\n\\nThe port to listen on (default: 5000).\\n\\n-w, \\\\--workers <workers>\\n    \\n\\nNumber of gunicorn worker processes to handle requests (default: 4).\\n\\n\\\\--static-prefix <static_prefix>\\n\\n    \\n\\nA prefix which will be prepended to the path of all static paths.\\n\\n\\\\--gunicorn-opts <gunicorn_opts>\\n\\n    \\n\\nAdditional command line options forwarded to gunicorn processes.\\n\\n\\\\--waitress-opts <waitress_opts>\\n\\n    \\n\\nAdditional command line options for waitress-serve.\\n\\n\\\\--expose-prometheus <expose_prometheus>\\n\\n    \\n\\nPath to the directory where metrics will be stored. If the directory doesn\\u00e2\\u0080\\u0099t\\nexist, it will be created. Activate prometheus exporter to expose metrics on\\n/metrics endpoint.\\n\\n\\\\--app-name <app_name>\\n\\n    \\n\\nApplication name to be used for the tracking server. If not specified,\\n\\u00e2\\u0080\\u0098mlflow.server:app\\u00e2\\u0080\\u0099 will be used.\\n\\nOptions\\n\\n    \\n\\nbasic-auth\\n\\n\\\\--dev\\n\\n    \\n\\nIf enabled, run the server with debug logging and auto-reload. Should only be\\nused for development purposes. Cannot be used with \\u00e2\\u0080\\u0098\\u00e2\\u0080\\u0093gunicorn-opts\\u00e2\\u0080\\u0099.\\nUnsupported on Windows.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 59203, \"end_char_idx\": 61242, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/cli.html","doc_id":"https://mlflow.org/docs/latest/cli.html","ref_doc_id":"https://mlflow.org/docs/latest/cli.html"}
{"_node_content":"{\"id_\": \"8dd9034c-232c-4b4f-82c0-ef0b7a093d9e\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/cli.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"2a8ac9b19cbddee491423c178812937f540f6dfd863499d20a2eb0461839b388\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"0122d3db-98a7-49ca-b8f1-6bcf4cbfbb32\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"311ef81753e5f78b386c6c465c84c7c4414dc7cc0639e5b5f7fbdb3bf7327f83\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"\\\\--gunicorn-opts <gunicorn_opts>\\n\\n    \\n\\nAdditional command line options forwarded to gunicorn processes.\\n\\n\\\\--waitress-opts <waitress_opts>\\n\\n    \\n\\nAdditional command line options for waitress-serve.\\n\\n\\\\--expose-prometheus <expose_prometheus>\\n\\n    \\n\\nPath to the directory where metrics will be stored. If the directory doesn\\u00e2\\u0080\\u0099t\\nexist, it will be created. Activate prometheus exporter to expose metrics on\\n/metrics endpoint.\\n\\n\\\\--app-name <app_name>\\n\\n    \\n\\nApplication name to be used for the tracking server. If not specified,\\n\\u00e2\\u0080\\u0098mlflow.server:app\\u00e2\\u0080\\u0099 will be used.\\n\\nOptions\\n\\n    \\n\\nbasic-auth\\n\\n\\\\--dev\\n\\n    \\n\\nIf enabled, run the server with debug logging and auto-reload. Should only be\\nused for development purposes. Cannot be used with \\u00e2\\u0080\\u0098\\u00e2\\u0080\\u0093gunicorn-opts\\u00e2\\u0080\\u0099.\\nUnsupported on Windows.\\n\\nDefault\\n\\n    \\n\\n`False`\\n\\nEnvironment variables\\n\\nMLFLOW_BACKEND_STORE_URI\\n\\n    \\n\\n> Provide a default for `--backend-store-uri`\\n\\nMLFLOW_REGISTRY_STORE_URI\\n\\n    \\n\\n> Provide a default for `--registry-store-uri`\\n\\nMLFLOW_DEFAULT_ARTIFACT_ROOT\\n\\n    \\n\\n> Provide a default for `--default-artifact-root`\\n\\nMLFLOW_SERVE_ARTIFACTS\\n\\n    \\n\\n> Provide a default for `--serve-artifacts`\\n\\nMLFLOW_ARTIFACTS_ONLY\\n\\n    \\n\\n> Provide a default for `--artifacts-only`\\n\\nMLFLOW_ARTIFACTS_DESTINATION\\n\\n    \\n\\n> Provide a default for `--artifacts-destination`\\n\\nMLFLOW_HOST\\n\\n    \\n\\n> Provide a default for `--host`\\n\\nMLFLOW_PORT\\n\\n    \\n\\n> Provide a default for `--port`\\n\\nMLFLOW_WORKERS\\n\\n    \\n\\n> Provide a default for `--workers`\\n\\nMLFLOW_STATIC_PREFIX\\n\\n    \\n\\n> Provide a default for `--static-prefix`\\n\\nMLFLOW_GUNICORN_OPTS\\n\\n    \\n\\n> Provide a default for `--gunicorn-opts`\\n\\nMLFLOW_EXPOSE_PROMETHEUS\\n\\n    \\n\\n> Provide a default for `--expose-prometheus`\\n\\n[ Previous](python_api/mlflow.xgboost.html \\\"mlflow.xgboost\\\") [Next\\n](auth/python-api.html \\\"MLflow Authentication Python API\\\")\\n\\n* * *\\n\\n(C) MLflow Project, a Series of LF Projects, LLC. All rights reserved.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 60462, \"end_char_idx\": 62363, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/cli.html","doc_id":"https://mlflow.org/docs/latest/cli.html","ref_doc_id":"https://mlflow.org/docs/latest/cli.html"}
{"_node_content":"{\"id_\": \"38601dea-e9c5-4245-a9bb-a234aa6dbe09\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/deep-learning/keras/quickstart/quickstart_keras.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"66af96b8a5a5e2a9450a6953a325de5aa0857ddbc581614eeef933515244f1f2\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"\", \"mimetype\": \"text/plain\", \"start_char_idx\": 0, \"end_char_idx\": 0, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/deep-learning/keras/quickstart/quickstart_keras.html","doc_id":"https://mlflow.org/docs/latest/deep-learning/keras/quickstart/quickstart_keras.html","ref_doc_id":"https://mlflow.org/docs/latest/deep-learning/keras/quickstart/quickstart_keras.html"}
{"_node_content":"{\"id_\": \"8ce2e0e8-654d-4e51-aa72-683c8aed54b1\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/deep-learning/pytorch/guide/index.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"66af96b8a5a5e2a9450a6953a325de5aa0857ddbc581614eeef933515244f1f2\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"\", \"mimetype\": \"text/plain\", \"start_char_idx\": 0, \"end_char_idx\": 0, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/deep-learning/pytorch/guide/index.html","doc_id":"https://mlflow.org/docs/latest/deep-learning/pytorch/guide/index.html","ref_doc_id":"https://mlflow.org/docs/latest/deep-learning/pytorch/guide/index.html"}
{"_node_content":"{\"id_\": \"5def8287-598d-4fa5-af61-bb9b0bbf2564\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/deep-learning/tensorflow/guide/index.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"66af96b8a5a5e2a9450a6953a325de5aa0857ddbc581614eeef933515244f1f2\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"\", \"mimetype\": \"text/plain\", \"start_char_idx\": 0, \"end_char_idx\": 0, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/deep-learning/tensorflow/guide/index.html","doc_id":"https://mlflow.org/docs/latest/deep-learning/tensorflow/guide/index.html","ref_doc_id":"https://mlflow.org/docs/latest/deep-learning/tensorflow/guide/index.html"}
{"_node_content":"{\"id_\": \"f905fe1d-33ec-42a5-b3a8-5f31fd376c83\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/deployment/index.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"66af96b8a5a5e2a9450a6953a325de5aa0857ddbc581614eeef933515244f1f2\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"\", \"mimetype\": \"text/plain\", \"start_char_idx\": 0, \"end_char_idx\": 0, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/deployment/index.html","doc_id":"https://mlflow.org/docs/latest/deployment/index.html","ref_doc_id":"https://mlflow.org/docs/latest/deployment/index.html"}
{"_node_content":"{\"id_\": \"750b9c20-2fca-423e-b923-3e54d8de413c\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/getting-started/intro-quickstart/index.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"66af96b8a5a5e2a9450a6953a325de5aa0857ddbc581614eeef933515244f1f2\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"\", \"mimetype\": \"text/plain\", \"start_char_idx\": 0, \"end_char_idx\": 0, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/getting-started/intro-quickstart/index.html","doc_id":"https://mlflow.org/docs/latest/getting-started/intro-quickstart/index.html","ref_doc_id":"https://mlflow.org/docs/latest/getting-started/intro-quickstart/index.html"}
{"_node_content":"{\"id_\": \"31121842-9d7c-4aa9-ac92-18038fd9b72c\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/index.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"ee625e9cd6c83ed6e1d9b184b6540f7ac7b754341208b254f61b745615374708\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"Skip to main content\\n\\n[![MLflow Logo](/docs/latest/images/logo-light.svg)![MLflow\\nLogo](/docs/latest/images/logo-dark.svg)](/docs/latest/)\\n\\nDocumentation\\n\\n  * [ML Docs](/docs/latest/ml/)\\n  * [GenAI Docs](/docs/latest/genai/)\\n\\n[API Reference](https://mlflow.org/docs/latest/api_reference/index.html)\\n\\n[GitHub](https://github.com/mlflow/mlflow)\\n\\nSearch\\n\\n# Documentation\\n\\nWelcome to the MLflow Documentation. Our documentation is organized into two\\nsections to help you find exactly what you need. Choose Model Training for\\ntraditional ML workflows, or select GenAI Apps & Agents for generative AI\\napplications, tracing, and evaluation tools.\\n\\n## Model Training\\n\\nAccess comprehensive guides for experiment tracking, model packaging, registry\\nmanagement, and deployment. Get started with MLflow's core functionality for\\ntraditional machine learning workflows, hyperparameter tuning, and model\\nlifecycle management.\\n\\n[Open Source \\u00e2\\u0086\\u0092](/docs/latest/ml/)[MLflow on Databricks\\n\\u00e2\\u0086\\u0092](https://docs.databricks.com/aws/en/mlflow/)\\n\\n## GenAI Apps & Agents\\n\\nExplore tools for GenAI tracing, prompt management, foundation model\\ndeployment, and evaluation frameworks. Learn how to track, evaluate, and\\noptimize your generative AI applications and agent workflows with MLflow.\\n\\n[Open Source \\u00e2\\u0086\\u0092](/docs/latest/genai/)[MLflow on Databricks\\n\\u00e2\\u0086\\u0092](https://docs.databricks.com/aws/en/mlflow3/genai/)\\n\\n\\u00c2\\u00a9 2025 MLflow Project, a Series of LF Projects, LLC.\\n\\n[Components](https://mlflow.org)\\n\\n[Releases](https://mlflow.org/releases)\\n\\n[Blog](https://mlflow.org/blog)\\n\\n[Docs](/docs/latest/)\\n\\n[Ambassador Program](https://mlflow.org/ambassadors)\", \"mimetype\": \"text/plain\", \"start_char_idx\": 0, \"end_char_idx\": 1615, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/index.html","doc_id":"https://mlflow.org/docs/latest/index.html","ref_doc_id":"https://mlflow.org/docs/latest/index.html"}
{"_node_content":"{\"id_\": \"89ff4044-a63d-42c1-b3af-ac157e2ed8ea\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/introduction/index.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"66af96b8a5a5e2a9450a6953a325de5aa0857ddbc581614eeef933515244f1f2\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"\", \"mimetype\": \"text/plain\", \"start_char_idx\": 0, \"end_char_idx\": 0, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/introduction/index.html","doc_id":"https://mlflow.org/docs/latest/introduction/index.html","ref_doc_id":"https://mlflow.org/docs/latest/introduction/index.html"}
{"_node_content":"{\"id_\": \"4432504a-7efa-4683-9617-14f9781c37d2\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/llms/custom-pyfunc-for-llms/index.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"66af96b8a5a5e2a9450a6953a325de5aa0857ddbc581614eeef933515244f1f2\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"\", \"mimetype\": \"text/plain\", \"start_char_idx\": 0, \"end_char_idx\": 0, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/llms/custom-pyfunc-for-llms/index.html","doc_id":"https://mlflow.org/docs/latest/llms/custom-pyfunc-for-llms/index.html","ref_doc_id":"https://mlflow.org/docs/latest/llms/custom-pyfunc-for-llms/index.html"}
{"_node_content":"{\"id_\": \"71758ce4-5bf7-4305-a88c-33feb8959b6f\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/llms/custom-pyfunc-for-llms/notebooks/custom-pyfunc-advanced-llm.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"66af96b8a5a5e2a9450a6953a325de5aa0857ddbc581614eeef933515244f1f2\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"\", \"mimetype\": \"text/plain\", \"start_char_idx\": 0, \"end_char_idx\": 0, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/llms/custom-pyfunc-for-llms/notebooks/custom-pyfunc-advanced-llm.html","doc_id":"https://mlflow.org/docs/latest/llms/custom-pyfunc-for-llms/notebooks/custom-pyfunc-advanced-llm.html","ref_doc_id":"https://mlflow.org/docs/latest/llms/custom-pyfunc-for-llms/notebooks/custom-pyfunc-advanced-llm.html"}
{"_node_content":"{\"id_\": \"37f51eca-45d6-43ef-a7fe-863bd35b700c\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/llms/custom-pyfunc-for-llms/notebooks/index.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"66af96b8a5a5e2a9450a6953a325de5aa0857ddbc581614eeef933515244f1f2\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"\", \"mimetype\": \"text/plain\", \"start_char_idx\": 0, \"end_char_idx\": 0, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/llms/custom-pyfunc-for-llms/notebooks/index.html","doc_id":"https://mlflow.org/docs/latest/llms/custom-pyfunc-for-llms/notebooks/index.html","ref_doc_id":"https://mlflow.org/docs/latest/llms/custom-pyfunc-for-llms/notebooks/index.html"}
{"_node_content":"{\"id_\": \"5ed91fc0-3952-474b-89ff-4b2c571b36b3\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/llms/deployments/guides/index.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"66af96b8a5a5e2a9450a6953a325de5aa0857ddbc581614eeef933515244f1f2\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"\", \"mimetype\": \"text/plain\", \"start_char_idx\": 0, \"end_char_idx\": 0, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/llms/deployments/guides/index.html","doc_id":"https://mlflow.org/docs/latest/llms/deployments/guides/index.html","ref_doc_id":"https://mlflow.org/docs/latest/llms/deployments/guides/index.html"}
{"_node_content":"{\"id_\": \"8dc8a090-24ad-402a-b0eb-f395f3562af8\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/llms/deployments/guides/step1-create-deployments.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"66af96b8a5a5e2a9450a6953a325de5aa0857ddbc581614eeef933515244f1f2\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"\", \"mimetype\": \"text/plain\", \"start_char_idx\": 0, \"end_char_idx\": 0, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/llms/deployments/guides/step1-create-deployments.html","doc_id":"https://mlflow.org/docs/latest/llms/deployments/guides/step1-create-deployments.html","ref_doc_id":"https://mlflow.org/docs/latest/llms/deployments/guides/step1-create-deployments.html"}
{"_node_content":"{\"id_\": \"192d8661-6223-4522-b12d-a8ff8d9026cb\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/llms/deployments/guides/step2-query-deployments.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"66af96b8a5a5e2a9450a6953a325de5aa0857ddbc581614eeef933515244f1f2\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"\", \"mimetype\": \"text/plain\", \"start_char_idx\": 0, \"end_char_idx\": 0, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/llms/deployments/guides/step2-query-deployments.html","doc_id":"https://mlflow.org/docs/latest/llms/deployments/guides/step2-query-deployments.html","ref_doc_id":"https://mlflow.org/docs/latest/llms/deployments/guides/step2-query-deployments.html"}
{"_node_content":"{\"id_\": \"fd513dcf-c20d-4897-a1ab-560beab9ad79\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/llms/deployments/index.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"66af96b8a5a5e2a9450a6953a325de5aa0857ddbc581614eeef933515244f1f2\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"\", \"mimetype\": \"text/plain\", \"start_char_idx\": 0, \"end_char_idx\": 0, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/llms/deployments/index.html","doc_id":"https://mlflow.org/docs/latest/llms/deployments/index.html","ref_doc_id":"https://mlflow.org/docs/latest/llms/deployments/index.html"}
{"_node_content":"{\"id_\": \"9b6d68fc-4c0a-4d6f-a09f-c7af367d0017\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/llms/deployments/uc_integration.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"66af96b8a5a5e2a9450a6953a325de5aa0857ddbc581614eeef933515244f1f2\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"\", \"mimetype\": \"text/plain\", \"start_char_idx\": 0, \"end_char_idx\": 0, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/llms/deployments/uc_integration.html","doc_id":"https://mlflow.org/docs/latest/llms/deployments/uc_integration.html","ref_doc_id":"https://mlflow.org/docs/latest/llms/deployments/uc_integration.html"}
{"_node_content":"{\"id_\": \"fb0b1cec-8e7e-4ad8-b01f-e2b426466f32\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/llms/index.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"66af96b8a5a5e2a9450a6953a325de5aa0857ddbc581614eeef933515244f1f2\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"\", \"mimetype\": \"text/plain\", \"start_char_idx\": 0, \"end_char_idx\": 0, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/llms/index.html","doc_id":"https://mlflow.org/docs/latest/llms/index.html","ref_doc_id":"https://mlflow.org/docs/latest/llms/index.html"}
{"_node_content":"{\"id_\": \"8efc7c71-d279-4169-bc4c-8f98b0f176c3\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/llms/langchain/autologging.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"66af96b8a5a5e2a9450a6953a325de5aa0857ddbc581614eeef933515244f1f2\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"\", \"mimetype\": \"text/plain\", \"start_char_idx\": 0, \"end_char_idx\": 0, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/llms/langchain/autologging.html","doc_id":"https://mlflow.org/docs/latest/llms/langchain/autologging.html","ref_doc_id":"https://mlflow.org/docs/latest/llms/langchain/autologging.html"}
{"_node_content":"{\"id_\": \"a66d70ad-3471-459b-83f1-d26963db0793\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/llms/langchain/guide/index.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"66af96b8a5a5e2a9450a6953a325de5aa0857ddbc581614eeef933515244f1f2\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"\", \"mimetype\": \"text/plain\", \"start_char_idx\": 0, \"end_char_idx\": 0, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/llms/langchain/guide/index.html","doc_id":"https://mlflow.org/docs/latest/llms/langchain/guide/index.html","ref_doc_id":"https://mlflow.org/docs/latest/llms/langchain/guide/index.html"}
{"_node_content":"{\"id_\": \"320ea38c-f972-40b1-a465-3cd47aaf7563\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/llms/langchain/index.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"66af96b8a5a5e2a9450a6953a325de5aa0857ddbc581614eeef933515244f1f2\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"\", \"mimetype\": \"text/plain\", \"start_char_idx\": 0, \"end_char_idx\": 0, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/llms/langchain/index.html","doc_id":"https://mlflow.org/docs/latest/llms/langchain/index.html","ref_doc_id":"https://mlflow.org/docs/latest/llms/langchain/index.html"}
{"_node_content":"{\"id_\": \"61064dfb-7019-43d9-a0f8-91226648ba47\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/llms/langchain/notebooks/langchain-quickstart.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"66af96b8a5a5e2a9450a6953a325de5aa0857ddbc581614eeef933515244f1f2\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"\", \"mimetype\": \"text/plain\", \"start_char_idx\": 0, \"end_char_idx\": 0, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/llms/langchain/notebooks/langchain-quickstart.html","doc_id":"https://mlflow.org/docs/latest/llms/langchain/notebooks/langchain-quickstart.html","ref_doc_id":"https://mlflow.org/docs/latest/llms/langchain/notebooks/langchain-quickstart.html"}
{"_node_content":"{\"id_\": \"0cf40dc8-36a2-4a3d-af15-d047ce4aecfb\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/llms/llama-index/index.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"66af96b8a5a5e2a9450a6953a325de5aa0857ddbc581614eeef933515244f1f2\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"\", \"mimetype\": \"text/plain\", \"start_char_idx\": 0, \"end_char_idx\": 0, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/llms/llama-index/index.html","doc_id":"https://mlflow.org/docs/latest/llms/llama-index/index.html","ref_doc_id":"https://mlflow.org/docs/latest/llms/llama-index/index.html"}
{"_node_content":"{\"id_\": \"0a763930-03c2-4813-a132-51527291ebb0\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/llms/llm-evaluate/index.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"66af96b8a5a5e2a9450a6953a325de5aa0857ddbc581614eeef933515244f1f2\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"\", \"mimetype\": \"text/plain\", \"start_char_idx\": 0, \"end_char_idx\": 0, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/llms/llm-evaluate/index.html","doc_id":"https://mlflow.org/docs/latest/llms/llm-evaluate/index.html","ref_doc_id":"https://mlflow.org/docs/latest/llms/llm-evaluate/index.html"}
{"_node_content":"{\"id_\": \"bcf0ff25-fe06-4aae-8bcd-3517432a3165\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/llms/openai/guide/index.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"66af96b8a5a5e2a9450a6953a325de5aa0857ddbc581614eeef933515244f1f2\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"\", \"mimetype\": \"text/plain\", \"start_char_idx\": 0, \"end_char_idx\": 0, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/llms/openai/guide/index.html","doc_id":"https://mlflow.org/docs/latest/llms/openai/guide/index.html","ref_doc_id":"https://mlflow.org/docs/latest/llms/openai/guide/index.html"}
{"_node_content":"{\"id_\": \"40951edc-9523-43cc-be99-1995b4e48988\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/llms/openai/index.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"66af96b8a5a5e2a9450a6953a325de5aa0857ddbc581614eeef933515244f1f2\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"\", \"mimetype\": \"text/plain\", \"start_char_idx\": 0, \"end_char_idx\": 0, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/llms/openai/index.html","doc_id":"https://mlflow.org/docs/latest/llms/openai/index.html","ref_doc_id":"https://mlflow.org/docs/latest/llms/openai/index.html"}
{"_node_content":"{\"id_\": \"423a859e-a61c-4d77-b736-ba6719d9721b\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/llms/sentence-transformers/guide/index.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"66af96b8a5a5e2a9450a6953a325de5aa0857ddbc581614eeef933515244f1f2\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"\", \"mimetype\": \"text/plain\", \"start_char_idx\": 0, \"end_char_idx\": 0, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/llms/sentence-transformers/guide/index.html","doc_id":"https://mlflow.org/docs/latest/llms/sentence-transformers/guide/index.html","ref_doc_id":"https://mlflow.org/docs/latest/llms/sentence-transformers/guide/index.html"}
{"_node_content":"{\"id_\": \"901686fe-f046-4019-9d1b-7a7ceeac2be9\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/llms/sentence-transformers/index.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"66af96b8a5a5e2a9450a6953a325de5aa0857ddbc581614eeef933515244f1f2\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"\", \"mimetype\": \"text/plain\", \"start_char_idx\": 0, \"end_char_idx\": 0, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/llms/sentence-transformers/index.html","doc_id":"https://mlflow.org/docs/latest/llms/sentence-transformers/index.html","ref_doc_id":"https://mlflow.org/docs/latest/llms/sentence-transformers/index.html"}
{"_node_content":"{\"id_\": \"eb7d2a2e-1ca0-4242-872c-ab06705c48e4\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/llms/tracing/index.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"66af96b8a5a5e2a9450a6953a325de5aa0857ddbc581614eeef933515244f1f2\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"\", \"mimetype\": \"text/plain\", \"start_char_idx\": 0, \"end_char_idx\": 0, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/llms/tracing/index.html","doc_id":"https://mlflow.org/docs/latest/llms/tracing/index.html","ref_doc_id":"https://mlflow.org/docs/latest/llms/tracing/index.html"}
{"_node_content":"{\"id_\": \"c563e31d-3609-44fc-96e8-75b7d93cc8ee\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/llms/tracing/overview.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"d1654e22a0c89d4d9aca27ad9e473913ba6700a5453076f6ae83aec03ca46792\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"`AccessDenied`Access Denied\", \"mimetype\": \"text/plain\", \"start_char_idx\": 0, \"end_char_idx\": 27, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/llms/tracing/overview.html","doc_id":"https://mlflow.org/docs/latest/llms/tracing/overview.html","ref_doc_id":"https://mlflow.org/docs/latest/llms/tracing/overview.html"}
{"_node_content":"{\"id_\": \"1637f8d1-a84f-4eb0-8151-4e91d2c5d621\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/llms/transformers/index.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"66af96b8a5a5e2a9450a6953a325de5aa0857ddbc581614eeef933515244f1f2\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"\", \"mimetype\": \"text/plain\", \"start_char_idx\": 0, \"end_char_idx\": 0, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/llms/transformers/index.html","doc_id":"https://mlflow.org/docs/latest/llms/transformers/index.html","ref_doc_id":"https://mlflow.org/docs/latest/llms/transformers/index.html"}
{"_node_content":"{\"id_\": \"076b7ac5-1f01-455b-ada7-68f3673f8cc4\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/model-evaluation/index.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"66af96b8a5a5e2a9450a6953a325de5aa0857ddbc581614eeef933515244f1f2\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"\", \"mimetype\": \"text/plain\", \"start_char_idx\": 0, \"end_char_idx\": 0, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/model-evaluation/index.html","doc_id":"https://mlflow.org/docs/latest/model-evaluation/index.html","ref_doc_id":"https://mlflow.org/docs/latest/model-evaluation/index.html"}
{"_node_content":"{\"id_\": \"13ff2087-b292-4a38-9e9d-2c4eb06f4fcc\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/model-registry.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"66af96b8a5a5e2a9450a6953a325de5aa0857ddbc581614eeef933515244f1f2\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"\", \"mimetype\": \"text/plain\", \"start_char_idx\": 0, \"end_char_idx\": 0, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/model-registry.html","doc_id":"https://mlflow.org/docs/latest/model-registry.html","ref_doc_id":"https://mlflow.org/docs/latest/model-registry.html"}
{"_node_content":"{\"id_\": \"9c44001d-2870-4ba9-bdd6-cf7e11c397e1\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/model/dependencies.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"66af96b8a5a5e2a9450a6953a325de5aa0857ddbc581614eeef933515244f1f2\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"\", \"mimetype\": \"text/plain\", \"start_char_idx\": 0, \"end_char_idx\": 0, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/model/dependencies.html","doc_id":"https://mlflow.org/docs/latest/model/dependencies.html","ref_doc_id":"https://mlflow.org/docs/latest/model/dependencies.html"}
{"_node_content":"{\"id_\": \"9456fe4f-b2ac-4300-bf45-1cb5f9c9f6dc\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/model/notebooks/signature_examples.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"66af96b8a5a5e2a9450a6953a325de5aa0857ddbc581614eeef933515244f1f2\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"\", \"mimetype\": \"text/plain\", \"start_char_idx\": 0, \"end_char_idx\": 0, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/model/notebooks/signature_examples.html","doc_id":"https://mlflow.org/docs/latest/model/notebooks/signature_examples.html","ref_doc_id":"https://mlflow.org/docs/latest/model/notebooks/signature_examples.html"}
{"_node_content":"{\"id_\": \"6e1e1e85-31f6-4a15-bece-6091ff7431c2\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/model/signatures.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"66af96b8a5a5e2a9450a6953a325de5aa0857ddbc581614eeef933515244f1f2\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"\", \"mimetype\": \"text/plain\", \"start_char_idx\": 0, \"end_char_idx\": 0, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/model/signatures.html","doc_id":"https://mlflow.org/docs/latest/model/signatures.html","ref_doc_id":"https://mlflow.org/docs/latest/model/signatures.html"}
{"_node_content":"{\"id_\": \"7f0f82a1-927b-4b82-a659-5b130bce81f2\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/models.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"66af96b8a5a5e2a9450a6953a325de5aa0857ddbc581614eeef933515244f1f2\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"\", \"mimetype\": \"text/plain\", \"start_char_idx\": 0, \"end_char_idx\": 0, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/models.html","doc_id":"https://mlflow.org/docs/latest/models.html","ref_doc_id":"https://mlflow.org/docs/latest/models.html"}
{"_node_content":"{\"id_\": \"df96927b-a3e2-499c-8ff8-d51bf97c5930\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/python_api/index.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"8b8adeb20b1431a69c7ab4ff7637919a571ad668814d5635b82f3c00ae826c96\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"6068bca0-aede-4590-875a-72281e627ce0\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"442898574e80664decfd0f824ca4b11c5fd07c1917c042f1f8bca0eb92eeb2cb\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"__[ ![MLflow](./_static/MLflow-logo-final-black.png) ](./index.html) [Main\\nDocs](/docs/latest) API Documentation\\n\\n3.1.0\\n\\n[Home](./index.html)\\n\\n  * Python API\\n    * [mlflow](mlflow.html)\\n    * [MLflow Tracing APIs](mlflow.html#mlflow-tracing-apis)\\n    * [MLflow Logged Model APIs](mlflow.html#mlflow-logged-model-apis)\\n    * [mlflow.ag2](mlflow.ag2.html)\\n    * [mlflow.anthropic](mlflow.anthropic.html)\\n    * [mlflow.artifacts](mlflow.artifacts.html)\\n    * [mlflow.autogen](mlflow.autogen.html)\\n    * [mlflow.bedrock](mlflow.bedrock.html)\\n    * [mlflow.catboost](mlflow.catboost.html)\\n    * [mlflow.client](mlflow.client.html)\\n    * [mlflow.config](mlflow.config.html)\\n    * [mlflow.crewai](mlflow.crewai.html)\\n    * [mlflow.data](mlflow.data.html)\\n    * [mlflow.deployments](mlflow.deployments.html)\\n    * [mlflow.diviner](mlflow.diviner.html)\\n    * [mlflow.dspy](mlflow.dspy.html)\\n    * [mlflow.entities](mlflow.entities.html)\\n    * [mlflow.environment_variables](mlflow.environment_variables.html)\\n    * [mlflow.gateway](mlflow.gateway.html)\\n    * [mlflow.gemini](mlflow.gemini.html)\\n    * [mlflow.genai](mlflow.genai.html)\\n    * [mlflow.groq](mlflow.groq.html)\\n    * [mlflow.h2o](mlflow.h2o.html)\\n    * [mlflow.johnsnowlabs](mlflow.johnsnowlabs.html)\\n    * [mlflow.keras](mlflow.keras.html)\\n    * [mlflow.langchain](mlflow.langchain.html)\\n    * [mlflow.lightgbm](mlflow.lightgbm.\", \"mimetype\": \"text/plain\", \"start_char_idx\": null, \"end_char_idx\": null, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/python_api/index.html","doc_id":"https://mlflow.org/docs/latest/python_api/index.html","ref_doc_id":"https://mlflow.org/docs/latest/python_api/index.html"}
{"_node_content":"{\"id_\": \"6068bca0-aede-4590-875a-72281e627ce0\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/python_api/index.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"8b8adeb20b1431a69c7ab4ff7637919a571ad668814d5635b82f3c00ae826c96\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"df96927b-a3e2-499c-8ff8-d51bf97c5930\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"85e442765328617daf30d3d6aa01abc84dd8e50963d4ab0869155f4b058c2c9a\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"09096965-661d-4414-9b7f-ce21df0d54de\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"d4103d4051a797ac511728e01b308e1d9739fd0467721b238d94406edb520eca\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"dspy](mlflow.dspy.html)\\n    * [mlflow.entities](mlflow.entities.html)\\n    * [mlflow.environment_variables](mlflow.environment_variables.html)\\n    * [mlflow.gateway](mlflow.gateway.html)\\n    * [mlflow.gemini](mlflow.gemini.html)\\n    * [mlflow.genai](mlflow.genai.html)\\n    * [mlflow.groq](mlflow.groq.html)\\n    * [mlflow.h2o](mlflow.h2o.html)\\n    * [mlflow.johnsnowlabs](mlflow.johnsnowlabs.html)\\n    * [mlflow.keras](mlflow.keras.html)\\n    * [mlflow.langchain](mlflow.langchain.html)\\n    * [mlflow.lightgbm](mlflow.lightgbm.html)\\n    * [mlflow.litellm](mlflow.litellm.html)\\n    * [mlflow.llama_index](mlflow.llama_index.html)\\n    * [mlflow.metrics](mlflow.metrics.html)\\n    * [mlflow.mistral](mlflow.mistral.html)\\n    * [mlflow.models](mlflow.models.html)\\n    * [mlflow.onnx](mlflow.onnx.html)\\n    * [mlflow.openai](mlflow.openai.html)\\n    * [mlflow.paddle](mlflow.paddle.html)\\n    * [mlflow.pmdarima](mlflow.pmdarima.html)\\n    * [mlflow.projects](mlflow.projects.html)\\n    * [mlflow.promptflow](mlflow.promptflow.html)\\n    * [mlflow.prophet](mlflow.prophet.html)\\n    * [mlflow.pydantic_ai](mlflow.pydantic_ai.html)\\n    * [mlflow.pyfunc](mlflow.pyfunc.html)\\n    * [mlflow.pyspark.ml](mlflow.pyspark.ml.html)\\n    * [mlflow.pytorch](mlflow.pytorch.html)\\n    * [mlflow.sagemaker](mlflow.sagemaker.html)\\n    * [mlflow.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 861, \"end_char_idx\": 2175, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/python_api/index.html","doc_id":"https://mlflow.org/docs/latest/python_api/index.html","ref_doc_id":"https://mlflow.org/docs/latest/python_api/index.html"}
{"_node_content":"{\"id_\": \"09096965-661d-4414-9b7f-ce21df0d54de\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/python_api/index.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"8b8adeb20b1431a69c7ab4ff7637919a571ad668814d5635b82f3c00ae826c96\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"6068bca0-aede-4590-875a-72281e627ce0\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"442898574e80664decfd0f824ca4b11c5fd07c1917c042f1f8bca0eb92eeb2cb\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"a5c77508-3372-431c-95a2-554d29e9caf4\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"571470392e86cffa8e8b88f964a5c9289ada990c6b9582967659faff1191ae24\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"openai.html)\\n    * [mlflow.paddle](mlflow.paddle.html)\\n    * [mlflow.pmdarima](mlflow.pmdarima.html)\\n    * [mlflow.projects](mlflow.projects.html)\\n    * [mlflow.promptflow](mlflow.promptflow.html)\\n    * [mlflow.prophet](mlflow.prophet.html)\\n    * [mlflow.pydantic_ai](mlflow.pydantic_ai.html)\\n    * [mlflow.pyfunc](mlflow.pyfunc.html)\\n    * [mlflow.pyspark.ml](mlflow.pyspark.ml.html)\\n    * [mlflow.pytorch](mlflow.pytorch.html)\\n    * [mlflow.sagemaker](mlflow.sagemaker.html)\\n    * [mlflow.sentence_transformers](mlflow.sentence_transformers.html)\\n    * [mlflow.server](mlflow.server.html)\\n    * [mlflow.shap](mlflow.shap.html)\\n    * [mlflow.sklearn](mlflow.sklearn.html)\\n    * [mlflow.smolagents](mlflow.smolagents.html)\\n    * [mlflow.spacy](mlflow.spacy.html)\\n    * [mlflow.spark](mlflow.spark.html)\\n    * [mlflow.statsmodels](mlflow.statsmodels.html)\\n    * [mlflow.system_metrics](mlflow.system_metrics.html)\\n    * [mlflow.tensorflow](mlflow.tensorflow.html)\\n    * [mlflow.tracing](mlflow.tracing.html)\\n    * [mlflow.transformers](mlflow.transformers.html)\\n    * [mlflow.types](mlflow.types.html)\\n    * [mlflow.utils](mlflow.utils.html)\\n    * [mlflow.xgboost](mlflow.xgboost.html)\\n    * Log Levels\\n  * [Command-Line Interface](./cli.html)\\n  * [MLflow Authentication Python API](./auth/python-api.html)\\n  * [MLflow Authentication REST API](./auth/rest-api.html)\\n  * [R API](./R-api.html)\\n  * [Java API](./java_api/index.\", \"mimetype\": \"text/plain\", \"start_char_idx\": null, \"end_char_idx\": null, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/python_api/index.html","doc_id":"https://mlflow.org/docs/latest/python_api/index.html","ref_doc_id":"https://mlflow.org/docs/latest/python_api/index.html"}
{"_node_content":"{\"id_\": \"a5c77508-3372-431c-95a2-554d29e9caf4\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/python_api/index.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"8b8adeb20b1431a69c7ab4ff7637919a571ad668814d5635b82f3c00ae826c96\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"09096965-661d-4414-9b7f-ce21df0d54de\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"d4103d4051a797ac511728e01b308e1d9739fd0467721b238d94406edb520eca\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"b3c9b7e1-649f-4d88-a8f6-811863e05fcc\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"aa39984f8dd6733cdd92e01ed7532c5b10f958930b89202d1f12c245f936af9d\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"statsmodels](mlflow.statsmodels.html)\\n    * [mlflow.system_metrics](mlflow.system_metrics.html)\\n    * [mlflow.tensorflow](mlflow.tensorflow.html)\\n    * [mlflow.tracing](mlflow.tracing.html)\\n    * [mlflow.transformers](mlflow.transformers.html)\\n    * [mlflow.types](mlflow.types.html)\\n    * [mlflow.utils](mlflow.utils.html)\\n    * [mlflow.xgboost](mlflow.xgboost.html)\\n    * Log Levels\\n  * [Command-Line Interface](./cli.html)\\n  * [MLflow Authentication Python API](./auth/python-api.html)\\n  * [MLflow Authentication REST API](./auth/rest-api.html)\\n  * [R API](./R-api.html)\\n  * [Java API](./java_api/index.html)\\n  * [REST API](./rest-api.html)\\n\\n[Contribute](https://github.com/mlflow/mlflow/blob/master/CONTRIBUTING.md)\\n\\n  * [Documentation](./index.html)\\n  * Python API\\n\\n# Python API\\n\\nThe MLflow Python API is organized into the following modules. The most common\\nfunctions are exposed in the [`mlflow`](mlflow.html#module-mlflow \\\"mlflow\\\")\\nmodule, so we recommend starting there.\\n\\n  * [mlflow](mlflow.html)\\n  * [MLflow Tracing APIs](mlflow.html#mlflow-tracing-apis)\\n  * [MLflow Logged Model APIs](mlflow.html#mlflow-logged-model-apis)\\n  * [mlflow.ag2](mlflow.ag2.html)\\n  * [mlflow.anthropic](mlflow.anthropic.html)\\n  * [mlflow.artifacts](mlflow.artifacts.html)\\n  * [mlflow.autogen](mlflow.autogen.html)\\n  * [mlflow.bedrock](mlflow.bedrock.html)\\n  * [mlflow.catboost](mlflow.catboost.html)\\n  * [mlflow.client](mlflow.client.html)\\n  * [mlflow.config](mlflow.config.html)\\n  * [mlflow.crewai](mlflow.crewai.html)\\n  * [mlflow.\", \"mimetype\": \"text/plain\", \"start_char_idx\": null, \"end_char_idx\": null, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/python_api/index.html","doc_id":"https://mlflow.org/docs/latest/python_api/index.html","ref_doc_id":"https://mlflow.org/docs/latest/python_api/index.html"}
{"_node_content":"{\"id_\": \"b3c9b7e1-649f-4d88-a8f6-811863e05fcc\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/python_api/index.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"8b8adeb20b1431a69c7ab4ff7637919a571ad668814d5635b82f3c00ae826c96\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"a5c77508-3372-431c-95a2-554d29e9caf4\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"571470392e86cffa8e8b88f964a5c9289ada990c6b9582967659faff1191ae24\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"433964e1-ee0c-44d2-9e21-fa743ed6cdff\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"1d3c89cffeabe032a575f56922734c163e1126fd3e8257e8ee419e9b2aeb7360\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"html)\\n  * [MLflow Tracing APIs](mlflow.html#mlflow-tracing-apis)\\n  * [MLflow Logged Model APIs](mlflow.html#mlflow-logged-model-apis)\\n  * [mlflow.ag2](mlflow.ag2.html)\\n  * [mlflow.anthropic](mlflow.anthropic.html)\\n  * [mlflow.artifacts](mlflow.artifacts.html)\\n  * [mlflow.autogen](mlflow.autogen.html)\\n  * [mlflow.bedrock](mlflow.bedrock.html)\\n  * [mlflow.catboost](mlflow.catboost.html)\\n  * [mlflow.client](mlflow.client.html)\\n  * [mlflow.config](mlflow.config.html)\\n  * [mlflow.crewai](mlflow.crewai.html)\\n  * [mlflow.data](mlflow.data.html)\\n  * [mlflow.deployments](mlflow.deployments.html)\\n  * [mlflow.diviner](mlflow.diviner.html)\\n  * [mlflow.dspy](mlflow.dspy.html)\\n  * [mlflow.entities](mlflow.entities.html)\\n  * [mlflow.environment_variables](mlflow.environment_variables.html)\\n  * [mlflow.gateway](mlflow.gateway.html)\\n  * [mlflow.gemini](mlflow.gemini.html)\\n  * [mlflow.genai](mlflow.genai.html)\\n  * [mlflow.groq](mlflow.groq.html)\\n  * [mlflow.h2o](mlflow.h2o.html)\\n  * [mlflow.johnsnowlabs](mlflow.johnsnowlabs.html)\\n  * [mlflow.keras](mlflow.keras.html)\\n  * [mlflow.langchain](mlflow.langchain.html)\\n  * [mlflow.lightgbm](mlflow.lightgbm.html)\\n  * [mlflow.litellm](mlflow.litellm.html)\\n  * [mlflow.llama_index](mlflow.llama_index.html)\\n  * [mlflow.metrics](mlflow.metrics.html)\\n  * [mlflow.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 3509, \"end_char_idx\": 4811, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/python_api/index.html","doc_id":"https://mlflow.org/docs/latest/python_api/index.html","ref_doc_id":"https://mlflow.org/docs/latest/python_api/index.html"}
{"_node_content":"{\"id_\": \"433964e1-ee0c-44d2-9e21-fa743ed6cdff\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/python_api/index.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"8b8adeb20b1431a69c7ab4ff7637919a571ad668814d5635b82f3c00ae826c96\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"b3c9b7e1-649f-4d88-a8f6-811863e05fcc\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"aa39984f8dd6733cdd92e01ed7532c5b10f958930b89202d1f12c245f936af9d\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"bd1c6062-f804-42d4-8ef5-feff9337b16a\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"9dbf8fe62c53fc350ee63e7ba4e23889f0c1a0fe6b935b0dcf5426066aac6e18\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"gemini](mlflow.gemini.html)\\n  * [mlflow.genai](mlflow.genai.html)\\n  * [mlflow.groq](mlflow.groq.html)\\n  * [mlflow.h2o](mlflow.h2o.html)\\n  * [mlflow.johnsnowlabs](mlflow.johnsnowlabs.html)\\n  * [mlflow.keras](mlflow.keras.html)\\n  * [mlflow.langchain](mlflow.langchain.html)\\n  * [mlflow.lightgbm](mlflow.lightgbm.html)\\n  * [mlflow.litellm](mlflow.litellm.html)\\n  * [mlflow.llama_index](mlflow.llama_index.html)\\n  * [mlflow.metrics](mlflow.metrics.html)\\n  * [mlflow.mistral](mlflow.mistral.html)\\n  * [mlflow.models](mlflow.models.html)\\n  * [mlflow.onnx](mlflow.onnx.html)\\n  * [mlflow.openai](mlflow.openai.html)\\n  * [mlflow.paddle](mlflow.paddle.html)\\n  * [mlflow.pmdarima](mlflow.pmdarima.html)\\n  * [mlflow.projects](mlflow.projects.html)\\n  * [mlflow.promptflow](mlflow.promptflow.html)\\n  * [mlflow.prophet](mlflow.prophet.html)\\n  * [mlflow.pydantic_ai](mlflow.pydantic_ai.html)\\n  * [mlflow.pyfunc](mlflow.pyfunc.html)\\n  * [mlflow.pyspark.ml](mlflow.pyspark.ml.html)\\n  * [mlflow.pytorch](mlflow.pytorch.html)\\n  * [mlflow.sagemaker](mlflow.sagemaker.html)\\n  * [mlflow.sentence_transformers](mlflow.sentence_transformers.html)\\n  * [mlflow.server](mlflow.server.html)\\n  * [mlflow.shap](mlflow.shap.html)\\n  * [mlflow.sklearn](mlflow.sklearn.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 4349, \"end_char_idx\": 5583, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/python_api/index.html","doc_id":"https://mlflow.org/docs/latest/python_api/index.html","ref_doc_id":"https://mlflow.org/docs/latest/python_api/index.html"}
{"_node_content":"{\"id_\": \"bd1c6062-f804-42d4-8ef5-feff9337b16a\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/python_api/index.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"8b8adeb20b1431a69c7ab4ff7637919a571ad668814d5635b82f3c00ae826c96\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"433964e1-ee0c-44d2-9e21-fa743ed6cdff\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"1d3c89cffeabe032a575f56922734c163e1126fd3e8257e8ee419e9b2aeb7360\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"c3d15cfc-a609-4ecf-8b3b-877b6177143b\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"bcae1e787f097a9d42435e949f9ccd5dc4e6593de4cff1294fd31a3bb8bbc6d2\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"html)\\n  * [mlflow.promptflow](mlflow.promptflow.html)\\n  * [mlflow.prophet](mlflow.prophet.html)\\n  * [mlflow.pydantic_ai](mlflow.pydantic_ai.html)\\n  * [mlflow.pyfunc](mlflow.pyfunc.html)\\n  * [mlflow.pyspark.ml](mlflow.pyspark.ml.html)\\n  * [mlflow.pytorch](mlflow.pytorch.html)\\n  * [mlflow.sagemaker](mlflow.sagemaker.html)\\n  * [mlflow.sentence_transformers](mlflow.sentence_transformers.html)\\n  * [mlflow.server](mlflow.server.html)\\n  * [mlflow.shap](mlflow.shap.html)\\n  * [mlflow.sklearn](mlflow.sklearn.html)\\n  * [mlflow.smolagents](mlflow.smolagents.html)\\n  * [mlflow.spacy](mlflow.spacy.html)\\n  * [mlflow.spark](mlflow.spark.html)\\n  * [mlflow.statsmodels](mlflow.statsmodels.html)\\n  * [mlflow.system_metrics](mlflow.system_metrics.html)\\n  * [mlflow.tensorflow](mlflow.tensorflow.html)\\n  * [mlflow.tracing](mlflow.tracing.html)\\n  * [mlflow.transformers](mlflow.transformers.html)\\n  * [mlflow.types](mlflow.types.html)\\n  * [mlflow.utils](mlflow.utils.html)\\n  * [mlflow.xgboost](mlflow.xgboost.html)\\n\\nSee also the [index of all functions and classes](./genindex.html).\\n\\n## Log Levels\\n\\nMLflow Python APIs log information during execution using the Python Logging\\nAPI. You can configure the log level for MLflow logs using the following code\\nsnippet. Learn more about Python log levels at the [Python language logging\\nguide](https://docs.python.org/3/howto/logging.html).\", \"mimetype\": \"text/plain\", \"start_char_idx\": null, \"end_char_idx\": null, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/python_api/index.html","doc_id":"https://mlflow.org/docs/latest/python_api/index.html","ref_doc_id":"https://mlflow.org/docs/latest/python_api/index.html"}
{"_node_content":"{\"id_\": \"c3d15cfc-a609-4ecf-8b3b-877b6177143b\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/python_api/index.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"8b8adeb20b1431a69c7ab4ff7637919a571ad668814d5635b82f3c00ae826c96\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"bd1c6062-f804-42d4-8ef5-feff9337b16a\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"9dbf8fe62c53fc350ee63e7ba4e23889f0c1a0fe6b935b0dcf5426066aac6e18\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"system_metrics](mlflow.system_metrics.html)\\n  * [mlflow.tensorflow](mlflow.tensorflow.html)\\n  * [mlflow.tracing](mlflow.tracing.html)\\n  * [mlflow.transformers](mlflow.transformers.html)\\n  * [mlflow.types](mlflow.types.html)\\n  * [mlflow.utils](mlflow.utils.html)\\n  * [mlflow.xgboost](mlflow.xgboost.html)\\n\\nSee also the [index of all functions and classes](./genindex.html).\\n\\n## Log Levels\\n\\nMLflow Python APIs log information during execution using the Python Logging\\nAPI. You can configure the log level for MLflow logs using the following code\\nsnippet. Learn more about Python log levels at the [Python language logging\\nguide](https://docs.python.org/3/howto/logging.html).\\n\\n    \\n    \\n    import logging\\n    \\n    logger = logging.getLogger(\\\"mlflow\\\")\\n    \\n    # Set log level to debugging\\n    logger.setLevel(logging.DEBUG)\\n    \\n\\n[ Previous](../index.html \\\"MLflow API Docs\\\") [Next ](mlflow.html \\\"mlflow\\\")\\n\\n* * *\\n\\n(C) MLflow Project, a Series of LF Projects, LLC. All rights reserved.\", \"mimetype\": \"text/plain\", \"start_char_idx\": null, \"end_char_idx\": null, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/python_api/index.html","doc_id":"https://mlflow.org/docs/latest/python_api/index.html","ref_doc_id":"https://mlflow.org/docs/latest/python_api/index.html"}
{"_node_content":"{\"id_\": \"0bd60346-d39f-4c11-8d33-d482d19af6ff\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/rest-api.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"9280e66609a7a65e141236963ad7b2533d22c543ba87cb1e924648644b79b5e4\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"9592f686-7585-46d2-9bb1-de9cf45cbc7a\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"f29fb3bd0ecb3da0aa6ec08c306e3934a9b6eb3058469d66b16908b9c739e4b4\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"__[ ![MLflow](_static/MLflow-logo-final-black.png) ](index.html) [Main\\nDocs](/docs/latest) API Documentation\\n\\n3.1.0\\n\\n[Home](index.html)\\n\\n  * [Python API](python_api/index.html)\\n  * [Command-Line Interface](cli.html)\\n  * [MLflow Authentication Python API](auth/python-api.html)\\n  * [MLflow Authentication REST API](auth/rest-api.html)\\n  * [R API](R-api.html)\\n  * [Java API](java_api/index.html)\\n  * REST API\\n    * Create Experiment\\n      * Request Structure\\n      * Response Structure\\n    * Search Experiments\\n      * Request Structure\\n      * Response Structure\\n    * Get Experiment\\n      * Request Structure\\n      * Response Structure\\n    * Get Experiment By Name\\n      * Request Structure\\n      * Response Structure\\n    * Delete Experiment\\n      * Request Structure\\n    * Restore Experiment\\n      * Request Structure\\n    * Update Experiment\\n      * Request Structure\\n    * Create Run\\n      * Request Structure\\n      * Response Structure\\n    * Delete Run\\n      * Request Structure\\n    * Restore Run\\n      * Request Structure\\n    * Get Run\\n      * Request Structure\\n      * Response Structure\\n    * Log Metric\\n      * Request Structure\\n    * Log Batch\\n      * Request Limits\\n      * Request Structure\\n    * Log Model\\n      * Request Structure\\n    * Log Inputs\\n      * Request Structure\\n    * Set Experiment Tag\\n      * Request Structure\\n    * Set Tag\\n      * Request Structure\\n    * Delete Tag\\n      * Request Structure\\n    * Log Param\\n      * Request Structure\\n    * Get Metric History\", \"mimetype\": \"text/plain\", \"start_char_idx\": 0, \"end_char_idx\": 1486, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/rest-api.html","doc_id":"https://mlflow.org/docs/latest/rest-api.html","ref_doc_id":"https://mlflow.org/docs/latest/rest-api.html"}
{"_node_content":"{\"id_\": \"9592f686-7585-46d2-9bb1-de9cf45cbc7a\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/rest-api.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"9280e66609a7a65e141236963ad7b2533d22c543ba87cb1e924648644b79b5e4\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"0bd60346-d39f-4c11-8d33-d482d19af6ff\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"5befd3331cef98ee7488c1091cb1d6e9d94c740d41a2429e2aa5bbc7f925a6c1\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"f94db417-01d7-4c88-979c-96d0e2591291\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"fd772fdaa65f1f6f84675b12ff7aee29fa45dce0ee31bd0ff462a6ad985e5e9e\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"* Request Structure\\n    * Restore Run\\n      * Request Structure\\n    * Get Run\\n      * Request Structure\\n      * Response Structure\\n    * Log Metric\\n      * Request Structure\\n    * Log Batch\\n      * Request Limits\\n      * Request Structure\\n    * Log Model\\n      * Request Structure\\n    * Log Inputs\\n      * Request Structure\\n    * Set Experiment Tag\\n      * Request Structure\\n    * Set Tag\\n      * Request Structure\\n    * Delete Tag\\n      * Request Structure\\n    * Log Param\\n      * Request Structure\\n    * Get Metric History\\n      * Request Structure\\n      * Response Structure\\n    * Search Runs\\n      * Request Structure\\n      * Response Structure\\n    * List Artifacts\\n      * Request Structure\\n      * Response Structure\\n    * Update Run\\n      * Request Structure\\n      * Response Structure\\n    * Create RegisteredModel\\n      * Request Structure\\n      * Response Structure\\n    * Get RegisteredModel\\n      * Request Structure\\n      * Response Structure\\n    * Rename RegisteredModel\\n      * Request Structure\\n      * Response Structure\\n    * Update RegisteredModel\\n      * Request Structure\\n      * Response Structure\\n    * Delete RegisteredModel\\n      * Request Structure\\n    * Get Latest ModelVersions\\n      * Request Structure\\n      * Response Structure\\n    * Create ModelVersion\\n      * Request Structure\\n      * Response Structure\\n    * Get ModelVersion\\n      * Request Structure\\n      * Response Structure\\n    * Update ModelVersion\\n      * Request Structure\", \"mimetype\": \"text/plain\", \"start_char_idx\": 962, \"end_char_idx\": 2425, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/rest-api.html","doc_id":"https://mlflow.org/docs/latest/rest-api.html","ref_doc_id":"https://mlflow.org/docs/latest/rest-api.html"}
{"_node_content":"{\"id_\": \"f94db417-01d7-4c88-979c-96d0e2591291\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/rest-api.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"9280e66609a7a65e141236963ad7b2533d22c543ba87cb1e924648644b79b5e4\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"9592f686-7585-46d2-9bb1-de9cf45cbc7a\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"f29fb3bd0ecb3da0aa6ec08c306e3934a9b6eb3058469d66b16908b9c739e4b4\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"0f139b0f-e309-49b2-9118-d86e7a02096d\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"e8399043fedbe6f8c33fa74ab06421a70307dc12ff2e8c23bd47f757f9a78028\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"* Response Structure\\n    * Get RegisteredModel\\n      * Request Structure\\n      * Response Structure\\n    * Rename RegisteredModel\\n      * Request Structure\\n      * Response Structure\\n    * Update RegisteredModel\\n      * Request Structure\\n      * Response Structure\\n    * Delete RegisteredModel\\n      * Request Structure\\n    * Get Latest ModelVersions\\n      * Request Structure\\n      * Response Structure\\n    * Create ModelVersion\\n      * Request Structure\\n      * Response Structure\\n    * Get ModelVersion\\n      * Request Structure\\n      * Response Structure\\n    * Update ModelVersion\\n      * Request Structure\\n      * Response Structure\\n    * Delete ModelVersion\\n      * Request Structure\\n    * Search ModelVersions\\n      * Request Structure\\n      * Response Structure\\n    * Get Download URI For ModelVersion Artifacts\\n      * Request Structure\\n      * Response Structure\\n    * Transition ModelVersion Stage\\n      * Request Structure\\n      * Response Structure\\n    * Search RegisteredModels\\n      * Request Structure\\n      * Response Structure\\n    * Set Registered Model Tag\\n      * Request Structure\\n    * Set Model Version Tag\\n      * Request Structure\\n    * Delete Registered Model Tag\\n      * Request Structure\\n    * Delete Model Version Tag\\n      * Request Structure\\n    * Delete Registered Model Alias\\n      * Request Structure\\n    * Get Model Version by Alias\\n      * Request Structure\\n      * Response Structure\\n    * Set Registered Model Alias\\n      * Request Structure\\n    * Data Structures\\n      * Dataset\\n      * DatasetInput\\n      * Experiment\\n      * ExperimentTag\", \"mimetype\": \"text/plain\", \"start_char_idx\": 1816, \"end_char_idx\": 3394, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/rest-api.html","doc_id":"https://mlflow.org/docs/latest/rest-api.html","ref_doc_id":"https://mlflow.org/docs/latest/rest-api.html"}
{"_node_content":"{\"id_\": \"0f139b0f-e309-49b2-9118-d86e7a02096d\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/rest-api.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"9280e66609a7a65e141236963ad7b2533d22c543ba87cb1e924648644b79b5e4\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"f94db417-01d7-4c88-979c-96d0e2591291\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"fd772fdaa65f1f6f84675b12ff7aee29fa45dce0ee31bd0ff462a6ad985e5e9e\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"0a70195c-52ce-4e19-b1e0-ce88865c88a6\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"917656b14d252e278b544d768c6afa2581bb8274dd9ffc8842405aadc027831a\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"* Request Structure\\n      * Response Structure\\n    * Set Registered Model Tag\\n      * Request Structure\\n    * Set Model Version Tag\\n      * Request Structure\\n    * Delete Registered Model Tag\\n      * Request Structure\\n    * Delete Model Version Tag\\n      * Request Structure\\n    * Delete Registered Model Alias\\n      * Request Structure\\n    * Get Model Version by Alias\\n      * Request Structure\\n      * Response Structure\\n    * Set Registered Model Alias\\n      * Request Structure\\n    * Data Structures\\n      * Dataset\\n      * DatasetInput\\n      * Experiment\\n      * ExperimentTag\\n      * FileInfo\\n      * InputTag\\n      * Metric\\n      * ModelInput\\n      * ModelMetric\\n      * ModelOutput\\n      * ModelParam\\n      * ModelVersion\\n      * ModelVersionDeploymentJobState\\n      * ModelVersionTag\\n      * Param\\n      * RegisteredModel\\n      * RegisteredModelAlias\\n      * RegisteredModelTag\\n      * Run\\n      * RunData\\n      * RunInfo\\n      * RunInputs\\n      * RunOutputs\\n      * RunTag\\n      * DeploymentJobRunState\\n      * ModelVersionStatus\\n      * RunStatus\\n      * State\\n      * ViewType\\n\\n[Contribute](https://github.com/mlflow/mlflow/blob/master/CONTRIBUTING.md)\\n\\n  * [Documentation](index.html)\\n  * REST API\\n\\n# REST API\\n\\nThe MLflow REST API allows you to create, list, and get experiments and runs,\\nand log parameters, metrics, and artifacts. The API is hosted under the `/api`\\nroute on the MLflow tracking server.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 2813, \"end_char_idx\": 4230, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/rest-api.html","doc_id":"https://mlflow.org/docs/latest/rest-api.html","ref_doc_id":"https://mlflow.org/docs/latest/rest-api.html"}
{"_node_content":"{\"id_\": \"0a70195c-52ce-4e19-b1e0-ce88865c88a6\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/rest-api.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"9280e66609a7a65e141236963ad7b2533d22c543ba87cb1e924648644b79b5e4\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"0f139b0f-e309-49b2-9118-d86e7a02096d\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"e8399043fedbe6f8c33fa74ab06421a70307dc12ff2e8c23bd47f757f9a78028\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"3bba1658-f4e2-4920-b60b-280c69348770\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"a914f2cbcbba63bf9013ee7ae4dfabad5ad9dd418b6bf1653f22e2bab74978be\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"* RegisteredModelTag\\n      * Run\\n      * RunData\\n      * RunInfo\\n      * RunInputs\\n      * RunOutputs\\n      * RunTag\\n      * DeploymentJobRunState\\n      * ModelVersionStatus\\n      * RunStatus\\n      * State\\n      * ViewType\\n\\n[Contribute](https://github.com/mlflow/mlflow/blob/master/CONTRIBUTING.md)\\n\\n  * [Documentation](index.html)\\n  * REST API\\n\\n# REST API\\n\\nThe MLflow REST API allows you to create, list, and get experiments and runs,\\nand log parameters, metrics, and artifacts. The API is hosted under the `/api`\\nroute on the MLflow tracking server. For example, to search for experiments on\\na tracking server hosted at `http://localhost:5000`, make a POST request to\\n`http://localhost:5000/api/2.0/mlflow/experiments/search`.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 3679, \"end_char_idx\": 4407, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/rest-api.html","doc_id":"https://mlflow.org/docs/latest/rest-api.html","ref_doc_id":"https://mlflow.org/docs/latest/rest-api.html"}
{"_node_content":"{\"id_\": \"3bba1658-f4e2-4920-b60b-280c69348770\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/rest-api.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"9280e66609a7a65e141236963ad7b2533d22c543ba87cb1e924648644b79b5e4\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"0a70195c-52ce-4e19-b1e0-ce88865c88a6\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"917656b14d252e278b544d768c6afa2581bb8274dd9ffc8842405aadc027831a\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"66be438b-49a1-48ae-a1b9-5257227b3d45\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"31568f6173ea27db4ca187ae595f023d5b9c3cfaa264ac9b9237c14662f23887\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"* RunOutputs\\n      * RunTag\\n      * DeploymentJobRunState\\n      * ModelVersionStatus\\n      * RunStatus\\n      * State\\n      * ViewType\\n\\n[Contribute](https://github.com/mlflow/mlflow/blob/master/CONTRIBUTING.md)\\n\\n  * [Documentation](index.html)\\n  * REST API\\n\\n# REST API\\n\\nThe MLflow REST API allows you to create, list, and get experiments and runs,\\nand log parameters, metrics, and artifacts. The API is hosted under the `/api`\\nroute on the MLflow tracking server. For example, to search for experiments on\\na tracking server hosted at `http://localhost:5000`, make a POST request to\\n`http://localhost:5000/api/2.0/mlflow/experiments/search`.\\n\\nTable of Contents\\n\\n  * Create Experiment\\n\\n  * Search Experiments\\n\\n  * Get Experiment\\n\\n  * Get Experiment By Name\\n\\n  * Delete Experiment\\n\\n  * Restore Experiment\\n\\n  * Update Experiment\\n\\n  * Create Run\\n\\n  * Delete Run\\n\\n  * Restore Run\\n\\n  * Get Run\\n\\n  * Log Metric\\n\\n  * Log Batch\\n\\n  * Log Model\\n\\n  * Log Inputs\\n\\n  * Set Experiment Tag\\n\\n  * Set Tag\\n\\n  * Delete Tag\\n\\n  * Log Param\\n\\n  * Get Metric History\\n\\n  * Search Runs\\n\\n  * List Artifacts\\n\\n  * Update Run\\n\\n  * Create RegisteredModel\\n\\n  * Get RegisteredModel\\n\\n  * Rename RegisteredModel\\n\\n  * Update RegisteredModel\\n\\n  * Delete RegisteredModel\\n\\n  * Get Latest ModelVersions\\n\\n  * Create ModelVersion\\n\\n  * Get ModelVersion\\n\\n  * Update ModelVersion\\n\\n  * Delete ModelVersion\\n\\n  * Search ModelVersions\\n\\n  * Get Download URI For ModelVersion Artifacts\\n\\n  * Transition ModelVersion Stage\\n\\n  * Search RegisteredModels\\n\\n  * Set Registered Model Tag\\n\\n  * Set Model Version Tag\\n\\n  * Delete Registered Model Tag\\n\\n  * Delete Model Version Tag\\n\\n  * Delete Registered Model Alias\\n\\n  * Get Model Version by Alias\\n\\n  * Set Registered Model Alias\\n\\n  * Data Structures\\n\\n* * *\\n\\n## Create Experiment\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/experiments/create` | `POST`  \\n  \\nCreate an experiment with a name.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 3768, \"end_char_idx\": 5648, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/rest-api.html","doc_id":"https://mlflow.org/docs/latest/rest-api.html","ref_doc_id":"https://mlflow.org/docs/latest/rest-api.html"}
{"_node_content":"{\"id_\": \"66be438b-49a1-48ae-a1b9-5257227b3d45\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/rest-api.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"9280e66609a7a65e141236963ad7b2533d22c543ba87cb1e924648644b79b5e4\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"3bba1658-f4e2-4920-b60b-280c69348770\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"a914f2cbcbba63bf9013ee7ae4dfabad5ad9dd418b6bf1653f22e2bab74978be\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"441c0196-d7d6-4d79-aac7-852f0a857745\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"a7e4825ff9e2bac8a9d99d95157c4f92d4462189b0cb18ef39819187622745c5\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"Returns the ID of the newly created\\nexperiment. Validates that another experiment with the same name does not\\nalready exist and fails if another experiment with the same name already\\nexists.\\n\\nThrows `RESOURCE_ALREADY_EXISTS` if a experiment with the given name exists.\\n\\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nname | `STRING` | Experiment name. This field is required.  \\nartifact_location | `STRING` | Location where all artifacts for the experiment are stored. If not provided, the remote server will select an appropriate default.  \\ntags | An array of ExperimentTag | A collection of tags to set on the experiment. Maximum tag size and number of tags per request depends on the storage backend. All storage backends are guaranteed to support tag keys up to 250 bytes in size and tag values up to 5000 bytes in size. All storage backends are also guaranteed to support up to 20 tags per request.  \\n  \\n### Response Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nexperiment_id | `STRING` | Unique identifier for the experiment.  \\n  \\n* * *\\n\\n## Search Experiments\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/experiments/search` | `POST`  \\n  \\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nmax_results | `INT64` | Maximum number of experiments desired. Servers may select a desired default max_results value. All servers are guaranteed to support a max_results threshold of at least 1,000 but may support more. Callers of this endpoint are encouraged to pass max_results explicitly and leverage page_token to iterate through experiments.  \\npage_token | `STRING` | Token indicating the page of experiments to fetch  \\nfilter | `STRING` | A filter expression over experiment attributes and tags that allows returning a subset of experiments. The syntax is a subset of SQL that supports ANDing together binary operations between an attribute or tag, and a constant. Example: `name LIKE 'test-%' AND tags.key = 'value'` You can select columns with special characters (hyphen, space, period, etc.) by using double quotes or backticks. Example: `tags.\\\"extra-key\\\" = 'value'` or `tags.`extra-key` = 'value'` Supported operators are `=`, `!=`, `LIKE`, and `ILIKE`.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 5649, \"end_char_idx\": 7870, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/rest-api.html","doc_id":"https://mlflow.org/docs/latest/rest-api.html","ref_doc_id":"https://mlflow.org/docs/latest/rest-api.html"}
{"_node_content":"{\"id_\": \"441c0196-d7d6-4d79-aac7-852f0a857745\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/rest-api.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"9280e66609a7a65e141236963ad7b2533d22c543ba87cb1e924648644b79b5e4\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"66be438b-49a1-48ae-a1b9-5257227b3d45\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"31568f6173ea27db4ca187ae595f023d5b9c3cfaa264ac9b9237c14662f23887\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"01ea7fbd-8e5d-41f3-b958-5995d5d99f51\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"b99fe1e73b7e8f8ed047bb9533db17d312958e26d178b2e6217c76cad2221919\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"All servers are guaranteed to support a max_results threshold of at least 1,000 but may support more. Callers of this endpoint are encouraged to pass max_results explicitly and leverage page_token to iterate through experiments.  \\npage_token | `STRING` | Token indicating the page of experiments to fetch  \\nfilter | `STRING` | A filter expression over experiment attributes and tags that allows returning a subset of experiments. The syntax is a subset of SQL that supports ANDing together binary operations between an attribute or tag, and a constant. Example: `name LIKE 'test-%' AND tags.key = 'value'` You can select columns with special characters (hyphen, space, period, etc.) by using double quotes or backticks. Example: `tags.\\\"extra-key\\\" = 'value'` or `tags.`extra-key` = 'value'` Supported operators are `=`, `!=`, `LIKE`, and `ILIKE`.  \\norder_by | An array of `STRING` | List of columns for ordering search results, which can include experiment name and id with an optional \\u00e2\\u0080\\u009cDESC\\u00e2\\u0080\\u009d or \\u00e2\\u0080\\u009cASC\\u00e2\\u0080\\u009d annotation, where \\u00e2\\u0080\\u009cASC\\u00e2\\u0080\\u009d is the default. Tiebreaks are done by experiment id DESC.  \\nview_type | ViewType | Qualifier for type of experiments to be returned. If unspecified, return only active experiments.  \\n  \\n### Response Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nexperiments | An array of Experiment | Experiments that match the search criteria  \\nnext_page_token | `STRING` | Token that can be used to retrieve the next page of experiments. An empty token means that no more experiments are available for retrieval.  \\n  \\n* * *\\n\\n## Get Experiment\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/experiments/get` | `GET`  \\n  \\nGet metadata for an experiment. This method works on deleted experiments.\\n\\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nexperiment_id | `STRING` | ID of the associated experiment. This field is required.  \\n  \\n### Response Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nexperiment | Experiment | Experiment details.  \\n  \\n* * *\\n\\n## Get Experiment By Name\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/experiments/get-by-name` | `GET`  \\n  \\nGet metadata for an experiment.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 7025, \"end_char_idx\": 9189, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/rest-api.html","doc_id":"https://mlflow.org/docs/latest/rest-api.html","ref_doc_id":"https://mlflow.org/docs/latest/rest-api.html"}
{"_node_content":"{\"id_\": \"01ea7fbd-8e5d-41f3-b958-5995d5d99f51\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/rest-api.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"9280e66609a7a65e141236963ad7b2533d22c543ba87cb1e924648644b79b5e4\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"441c0196-d7d6-4d79-aac7-852f0a857745\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"a7e4825ff9e2bac8a9d99d95157c4f92d4462189b0cb18ef39819187622745c5\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"1e8be666-a668-4da9-858d-ba8922ec5a96\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"136fbb11683bd94dc9032dcf1522ddf30a817933d66496bbfd701572963d876a\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"An empty token means that no more experiments are available for retrieval.  \\n  \\n* * *\\n\\n## Get Experiment\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/experiments/get` | `GET`  \\n  \\nGet metadata for an experiment. This method works on deleted experiments.\\n\\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nexperiment_id | `STRING` | ID of the associated experiment. This field is required.  \\n  \\n### Response Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nexperiment | Experiment | Experiment details.  \\n  \\n* * *\\n\\n## Get Experiment By Name\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/experiments/get-by-name` | `GET`  \\n  \\nGet metadata for an experiment.\\n\\nThis endpoint will return deleted experiments, but prefers the active\\nexperiment if an active and deleted experiment share the same name. If\\nmultiple deleted experiments share the same name, the API will return one of\\nthem.\\n\\nThrows `RESOURCE_DOES_NOT_EXIST` if no experiment with the specified name\\nexists.\\n\\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nexperiment_name | `STRING` | Name of the associated experiment. This field is required.  \\n  \\n### Response Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nexperiment | Experiment | Experiment details.  \\n  \\n* * *\\n\\n## Delete Experiment\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/experiments/delete` | `POST`  \\n  \\nMark an experiment and associated metadata, runs, metrics, params, and tags\\nfor deletion. If the experiment uses FileStore, artifacts associated with\\nexperiment are also deleted.\\n\\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nexperiment_id | `STRING` | ID of the associated experiment. This field is required.  \\n  \\n* * *\\n\\n## Restore Experiment\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/experiments/restore` | `POST`  \\n  \\nRestore an experiment marked for deletion. This also restores associated\\nmetadata, runs, metrics, params, and tags. If experiment uses FileStore,\\nunderlying artifacts associated with experiment are also restored.\\n\\nThrows `RESOURCE_DOES_NOT_EXIST` if experiment was never created or was\\npermanently deleted.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 8498, \"end_char_idx\": 10654, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/rest-api.html","doc_id":"https://mlflow.org/docs/latest/rest-api.html","ref_doc_id":"https://mlflow.org/docs/latest/rest-api.html"}
{"_node_content":"{\"id_\": \"1e8be666-a668-4da9-858d-ba8922ec5a96\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/rest-api.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"9280e66609a7a65e141236963ad7b2533d22c543ba87cb1e924648644b79b5e4\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"01ea7fbd-8e5d-41f3-b958-5995d5d99f51\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"b99fe1e73b7e8f8ed047bb9533db17d312958e26d178b2e6217c76cad2221919\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"302b5d01-e708-4b26-837f-0b16dbbd2436\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"5c2ebe6cc1684f040d2b6080f21b8f9a8cc300308fc71e4c4cdacd845d041533\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"* * *\\n\\n## Delete Experiment\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/experiments/delete` | `POST`  \\n  \\nMark an experiment and associated metadata, runs, metrics, params, and tags\\nfor deletion. If the experiment uses FileStore, artifacts associated with\\nexperiment are also deleted.\\n\\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nexperiment_id | `STRING` | ID of the associated experiment. This field is required.  \\n  \\n* * *\\n\\n## Restore Experiment\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/experiments/restore` | `POST`  \\n  \\nRestore an experiment marked for deletion. This also restores associated\\nmetadata, runs, metrics, params, and tags. If experiment uses FileStore,\\nunderlying artifacts associated with experiment are also restored.\\n\\nThrows `RESOURCE_DOES_NOT_EXIST` if experiment was never created or was\\npermanently deleted.\\n\\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nexperiment_id | `STRING` | ID of the associated experiment. This field is required.  \\n  \\n* * *\\n\\n## Update Experiment\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/experiments/update` | `POST`  \\n  \\nUpdate experiment metadata.\\n\\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nexperiment_id | `STRING` | ID of the associated experiment. This field is required.  \\nnew_name | `STRING` | If provided, the experiment\\u00e2\\u0080\\u0099s name is changed to the new name. The new name must be unique.  \\n  \\n* * *\\n\\n## Create Run\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/runs/create` | `POST`  \\n  \\nCreate a new run within an experiment. A run is usually a single execution of\\na machine learning or data ETL pipeline. MLflow uses runs to track Param,\\nMetric, and RunTag associated with a single execution.\\n\\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nexperiment_id | `STRING` | ID of the associated experiment.  \\nuser_id | `STRING` | ID of the user executing the run. This field is deprecated as of MLflow 1.0, and will be removed in a future MLflow release. Use \\u00e2\\u0080\\u0098mlflow.user\\u00e2\\u0080\\u0099 tag instead.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 9786, \"end_char_idx\": 11850, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/rest-api.html","doc_id":"https://mlflow.org/docs/latest/rest-api.html","ref_doc_id":"https://mlflow.org/docs/latest/rest-api.html"}
{"_node_content":"{\"id_\": \"302b5d01-e708-4b26-837f-0b16dbbd2436\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/rest-api.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"9280e66609a7a65e141236963ad7b2533d22c543ba87cb1e924648644b79b5e4\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"1e8be666-a668-4da9-858d-ba8922ec5a96\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"136fbb11683bd94dc9032dcf1522ddf30a817933d66496bbfd701572963d876a\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"38b8403f-bed8-4135-8be1-8c7506af591c\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"331857c4efcb6b68d0f41ef1dd1edf5cc744b2e0f78a227f5399dce0bff44e70\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"new_name | `STRING` | If provided, the experiment\\u00e2\\u0080\\u0099s name is changed to the new name. The new name must be unique.  \\n  \\n* * *\\n\\n## Create Run\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/runs/create` | `POST`  \\n  \\nCreate a new run within an experiment. A run is usually a single execution of\\na machine learning or data ETL pipeline. MLflow uses runs to track Param,\\nMetric, and RunTag associated with a single execution.\\n\\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nexperiment_id | `STRING` | ID of the associated experiment.  \\nuser_id | `STRING` | ID of the user executing the run. This field is deprecated as of MLflow 1.0, and will be removed in a future MLflow release. Use \\u00e2\\u0080\\u0098mlflow.user\\u00e2\\u0080\\u0099 tag instead.  \\nrun_name | `STRING` | Name of the run.  \\nstart_time | `INT64` | Unix timestamp in milliseconds of when the run started.  \\ntags | An array of RunTag | Additional metadata for run.  \\n  \\n### Response Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nrun | Run | The newly created run.  \\n  \\n* * *\\n\\n## Delete Run\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/runs/delete` | `POST`  \\n  \\nMark a run for deletion.\\n\\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nrun_id | `STRING` | ID of the run to delete. This field is required.  \\n  \\n* * *\\n\\n## Restore Run\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/runs/restore` | `POST`  \\n  \\nRestore a deleted run.\\n\\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nrun_id | `STRING` | ID of the run to restore. This field is required.  \\n  \\n* * *\\n\\n## Get Run\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/runs/get` | `GET`  \\n  \\nGet metadata, metrics, params, and tags for a run. In the case where multiple\\nmetrics with the same key are logged for a run, return only the value with the\\nlatest timestamp. If there are multiple values with the latest timestamp,\\nreturn the maximum of these values.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 11112, \"end_char_idx\": 13039, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/rest-api.html","doc_id":"https://mlflow.org/docs/latest/rest-api.html","ref_doc_id":"https://mlflow.org/docs/latest/rest-api.html"}
{"_node_content":"{\"id_\": \"38b8403f-bed8-4135-8be1-8c7506af591c\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/rest-api.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"9280e66609a7a65e141236963ad7b2533d22c543ba87cb1e924648644b79b5e4\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"302b5d01-e708-4b26-837f-0b16dbbd2436\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"5c2ebe6cc1684f040d2b6080f21b8f9a8cc300308fc71e4c4cdacd845d041533\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"c9b63e6c-0843-41a4-91f3-32a08b15a746\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"36398e473c8e389bf2b9014e0f17ba5a8111634fe2f084a84cccfac4b888c952\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"This field is required.  \\n  \\n* * *\\n\\n## Restore Run\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/runs/restore` | `POST`  \\n  \\nRestore a deleted run.\\n\\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nrun_id | `STRING` | ID of the run to restore. This field is required.  \\n  \\n* * *\\n\\n## Get Run\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/runs/get` | `GET`  \\n  \\nGet metadata, metrics, params, and tags for a run. In the case where multiple\\nmetrics with the same key are logged for a run, return only the value with the\\nlatest timestamp. If there are multiple values with the latest timestamp,\\nreturn the maximum of these values.\\n\\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nrun_id | `STRING` | ID of the run to fetch. Must be provided.  \\nrun_uuid | `STRING` | [Deprecated, use run_id instead] ID of the run to fetch. This field will be removed in a future MLflow version.  \\n  \\n### Response Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nrun | Run | Run metadata (name, start time, etc) and data (metrics, params, and tags).  \\n  \\n* * *\\n\\n## Log Metric\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/runs/log-metric` | `POST`  \\n  \\nLog a metric for a run. A metric is a key-value pair (string key, float value)\\nwith an associated timestamp. Examples include the various metrics that\\nrepresent ML model accuracy. A metric can be logged multiple times.\\n\\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nrun_id | `STRING` | ID of the run under which to log the metric. Must be provided.  \\nrun_uuid | `STRING` | [Deprecated, use run_id instead] ID of the run under which to log the metric. This field will be removed in a future MLflow version.  \\nkey | `STRING` | Name of the metric. This field is required.  \\nvalue | `DOUBLE` | Double value of the metric being logged. This field is required.  \\ntimestamp | `INT64` | Unix timestamp in milliseconds at the time metric was logged.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 12387, \"end_char_idx\": 14344, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/rest-api.html","doc_id":"https://mlflow.org/docs/latest/rest-api.html","ref_doc_id":"https://mlflow.org/docs/latest/rest-api.html"}
{"_node_content":"{\"id_\": \"c9b63e6c-0843-41a4-91f3-32a08b15a746\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/rest-api.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"9280e66609a7a65e141236963ad7b2533d22c543ba87cb1e924648644b79b5e4\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"38b8403f-bed8-4135-8be1-8c7506af591c\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"331857c4efcb6b68d0f41ef1dd1edf5cc744b2e0f78a227f5399dce0bff44e70\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"fa76181a-caa9-4a0e-b216-d38bde8d124f\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"dce48c060c97f1c595a81a692258cc59b9ab53bf3f2f7afde75b903ace8da115\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"A metric is a key-value pair (string key, float value)\\nwith an associated timestamp. Examples include the various metrics that\\nrepresent ML model accuracy. A metric can be logged multiple times.\\n\\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nrun_id | `STRING` | ID of the run under which to log the metric. Must be provided.  \\nrun_uuid | `STRING` | [Deprecated, use run_id instead] ID of the run under which to log the metric. This field will be removed in a future MLflow version.  \\nkey | `STRING` | Name of the metric. This field is required.  \\nvalue | `DOUBLE` | Double value of the metric being logged. This field is required.  \\ntimestamp | `INT64` | Unix timestamp in milliseconds at the time metric was logged. This field is required.  \\nstep | `INT64` | Step at which to log the metric  \\n  \\n* * *\\n\\n## Log Batch\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/runs/log-batch` | `POST`  \\n  \\nLog a batch of metrics, params, and tags for a run. If any data failed to be\\npersisted, the server will respond with an error (non-200 status code). In\\ncase of error (due to internal server error or an invalid request), partial\\ndata may be written.\\n\\nYou can write metrics, params, and tags in interleaving fashion, but within a\\ngiven entity type are guaranteed to follow the order specified in the request\\nbody.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 13603, \"end_char_idx\": 14934, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/rest-api.html","doc_id":"https://mlflow.org/docs/latest/rest-api.html","ref_doc_id":"https://mlflow.org/docs/latest/rest-api.html"}
{"_node_content":"{\"id_\": \"fa76181a-caa9-4a0e-b216-d38bde8d124f\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/rest-api.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"9280e66609a7a65e141236963ad7b2533d22c543ba87cb1e924648644b79b5e4\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"c9b63e6c-0843-41a4-91f3-32a08b15a746\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"36398e473c8e389bf2b9014e0f17ba5a8111634fe2f084a84cccfac4b888c952\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"7181df44-5983-4673-a0bf-4db0651520a1\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"90dc7ed11d09190804b59d5c8d7c798566f5fc6133a8ccbbba32b9a0a5b9a007\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"This field is required.  \\nvalue | `DOUBLE` | Double value of the metric being logged. This field is required.  \\ntimestamp | `INT64` | Unix timestamp in milliseconds at the time metric was logged. This field is required.  \\nstep | `INT64` | Step at which to log the metric  \\n  \\n* * *\\n\\n## Log Batch\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/runs/log-batch` | `POST`  \\n  \\nLog a batch of metrics, params, and tags for a run. If any data failed to be\\npersisted, the server will respond with an error (non-200 status code). In\\ncase of error (due to internal server error or an invalid request), partial\\ndata may be written.\\n\\nYou can write metrics, params, and tags in interleaving fashion, but within a\\ngiven entity type are guaranteed to follow the order specified in the request\\nbody. That is, for an API request like\\n\\n    \\n    \\n    {\\n       \\\"run_id\\\": \\\"2a14ed5c6a87499199e0106c3501eab8\\\",\\n       \\\"metrics\\\": [\\n         {\\\"key\\\": \\\"mae\\\", \\\"value\\\": 2.5, \\\"timestamp\\\": 1552550804},\\n         {\\\"key\\\": \\\"rmse\\\", \\\"value\\\": 2.7, \\\"timestamp\\\": 1552550804},\\n       ],\\n       \\\"params\\\": [\\n         {\\\"key\\\": \\\"model_class\\\", \\\"value\\\": \\\"LogisticRegression\\\"},\\n       ]\\n    }\\n    \\n\\nthe server is guaranteed to write metric \\u00e2\\u0080\\u009crmse\\u00e2\\u0080\\u009d after \\u00e2\\u0080\\u009cmae\\u00e2\\u0080\\u009d, though it\\nmay write param \\u00e2\\u0080\\u009cmodel_class\\u00e2\\u0080\\u009d before both metrics, after \\u00e2\\u0080\\u009cmae\\u00e2\\u0080\\u009d, or\\nafter both metrics.\\n\\nThe overwrite behavior for metrics, params, and tags is as follows:\\n\\n  * Metrics: metric values are never overwritten. Logging a metric (key, value, timestamp) appends to the set of values for the metric with the provided key.\\n\\n  * Tags: tag values can be overwritten by successive writes to the same tag key. That is, if multiple tag values with the same key are provided in the same API request, the last-provided tag value is written. Logging the same tag (key, value) is permitted - that is, logging a tag is idempotent.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 14149, \"end_char_idx\": 16000, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/rest-api.html","doc_id":"https://mlflow.org/docs/latest/rest-api.html","ref_doc_id":"https://mlflow.org/docs/latest/rest-api.html"}
{"_node_content":"{\"id_\": \"7181df44-5983-4673-a0bf-4db0651520a1\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/rest-api.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"9280e66609a7a65e141236963ad7b2533d22c543ba87cb1e924648644b79b5e4\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"fa76181a-caa9-4a0e-b216-d38bde8d124f\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"dce48c060c97f1c595a81a692258cc59b9ab53bf3f2f7afde75b903ace8da115\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"3c2e98d1-6c3e-46ea-ac73-3a69ebe31ea5\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"eaf935ed56579d51515b8d7506ba8010743671122daac17360fb5ea6e525406f\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"The overwrite behavior for metrics, params, and tags is as follows:\\n\\n  * Metrics: metric values are never overwritten. Logging a metric (key, value, timestamp) appends to the set of values for the metric with the provided key.\\n\\n  * Tags: tag values can be overwritten by successive writes to the same tag key. That is, if multiple tag values with the same key are provided in the same API request, the last-provided tag value is written. Logging the same tag (key, value) is permitted - that is, logging a tag is idempotent.\\n\\n  * Params: once written, param values cannot be changed (attempting to overwrite a param value will result in an error). However, logging the same param (key, value) is permitted - that is, logging a param is idempotent.\\n\\n### Request Limits\\n\\nA single JSON-serialized API request may be up to 1 MB in size and contain:\\n\\n  * No more than 1000 metrics, params, and tags in total\\n\\n  * Up to 1000 metrics\\n\\n  * Up to 100 params\\n\\n  * Up to 100 tags\\n\\nFor example, a valid request might contain 900 metrics, 50 params, and 50\\ntags, but logging 900 metrics, 50 params, and 51 tags is invalid. The\\nfollowing limits also apply to metric, param, and tag keys and values:\\n\\n  * Metric, param, and tag keys can be up to 250 characters in length\\n\\n  * Param and tag values can be up to 250 characters in length\\n\\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nrun_id | `STRING` | ID of the run to log under  \\nmetrics | An array of Metric | Metrics to log. A single request can contain up to 1000 metrics, and up to 1000 metrics, params, and tags in total.  \\nparams | An array of Param | Params to log. A single request can contain up to 100 params, and up to 1000 metrics, params, and tags in total.  \\ntags | An array of RunTag | Tags to log. A single request can contain up to 100 tags, and up to 1000 metrics, params, and tags in total.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 15476, \"end_char_idx\": 17346, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/rest-api.html","doc_id":"https://mlflow.org/docs/latest/rest-api.html","ref_doc_id":"https://mlflow.org/docs/latest/rest-api.html"}
{"_node_content":"{\"id_\": \"3c2e98d1-6c3e-46ea-ac73-3a69ebe31ea5\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/rest-api.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"9280e66609a7a65e141236963ad7b2533d22c543ba87cb1e924648644b79b5e4\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"7181df44-5983-4673-a0bf-4db0651520a1\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"90dc7ed11d09190804b59d5c8d7c798566f5fc6133a8ccbbba32b9a0a5b9a007\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"dbe695be-edb1-4ffd-981f-895350db3efd\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"88e58ce61de9431c4ba5130f74f328e0011a3307975ac5cd532750bbc92893dc\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"A single request can contain up to 1000 metrics, and up to 1000 metrics, params, and tags in total.  \\nparams | An array of Param | Params to log. A single request can contain up to 100 params, and up to 1000 metrics, params, and tags in total.  \\ntags | An array of RunTag | Tags to log. A single request can contain up to 100 tags, and up to 1000 metrics, params, and tags in total.  \\n  \\n* * *\\n\\n## Log Model\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/runs/log-model` | `POST`  \\n  \\nNote\\n\\nExperimental: This API may change or be removed in a future release without\\nwarning.\\n\\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nrun_id | `STRING` | ID of the run to log under  \\nmodel_json | `STRING` | MLmodel file in json format.  \\n  \\n* * *\\n\\n## Log Inputs\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/runs/log-inputs` | `POST`  \\n  \\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nrun_id | `STRING` | ID of the run to log under This field is required.  \\ndatasets | An array of DatasetInput | Dataset inputs  \\n  \\n* * *\\n\\n## Set Experiment Tag\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/experiments/set-experiment-tag` | `POST`  \\n  \\nSet a tag on an experiment. Experiment tags are metadata that can be updated.\\n\\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nexperiment_id | `STRING` | ID of the experiment under which to log the tag. Must be provided. This field is required.  \\nkey | `STRING` | Name of the tag. Maximum size depends on storage backend. All storage backends are guaranteed to support key values up to 250 bytes in size. This field is required.  \\nvalue | `STRING` | String value of the tag being logged. Maximum size depends on storage backend. All storage backends are guaranteed to support key values up to 5000 bytes in size. This field is required.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 16964, \"end_char_idx\": 18804, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/rest-api.html","doc_id":"https://mlflow.org/docs/latest/rest-api.html","ref_doc_id":"https://mlflow.org/docs/latest/rest-api.html"}
{"_node_content":"{\"id_\": \"dbe695be-edb1-4ffd-981f-895350db3efd\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/rest-api.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"9280e66609a7a65e141236963ad7b2533d22c543ba87cb1e924648644b79b5e4\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"3c2e98d1-6c3e-46ea-ac73-3a69ebe31ea5\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"eaf935ed56579d51515b8d7506ba8010743671122daac17360fb5ea6e525406f\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"01c59341-e34a-4e25-bfed-e604e5127821\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"d827e8043d06c6833dde2c2207906d99ffb32ba466ed3b3e4c7d37adf0941243\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"Experiment tags are metadata that can be updated.\\n\\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nexperiment_id | `STRING` | ID of the experiment under which to log the tag. Must be provided. This field is required.  \\nkey | `STRING` | Name of the tag. Maximum size depends on storage backend. All storage backends are guaranteed to support key values up to 250 bytes in size. This field is required.  \\nvalue | `STRING` | String value of the tag being logged. Maximum size depends on storage backend. All storage backends are guaranteed to support key values up to 5000 bytes in size. This field is required.  \\n  \\n* * *\\n\\n## Set Tag\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/runs/set-tag` | `POST`  \\n  \\nSet a tag on a run. Tags are run metadata that can be updated during a run and\\nafter a run completes.\\n\\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nrun_id | `STRING` | ID of the run under which to log the tag. Must be provided.  \\nrun_uuid | `STRING` | [Deprecated, use run_id instead] ID of the run under which to log the tag. This field will be removed in a future MLflow version.  \\nkey | `STRING` | Name of the tag. Maximum size depends on storage backend. All storage backends are guaranteed to support key values up to 250 bytes in size. This field is required.  \\nvalue | `STRING` | String value of the tag being logged. Maximum size depends on storage backend. All storage backends are guaranteed to support key values up to 5000 bytes in size. This field is required.  \\n  \\n* * *\\n\\n## Delete Tag\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/runs/delete-tag` | `POST`  \\n  \\nDelete a tag on a run. Tags are run metadata that can be updated during a run\\nand after a run completes.\\n\\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nrun_id | `STRING` | ID of the run that the tag was logged under. Must be provided. This field is required.  \\nkey | `STRING` | Name of the tag. Maximum size is 255 bytes.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 18173, \"end_char_idx\": 20155, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/rest-api.html","doc_id":"https://mlflow.org/docs/latest/rest-api.html","ref_doc_id":"https://mlflow.org/docs/latest/rest-api.html"}
{"_node_content":"{\"id_\": \"01c59341-e34a-4e25-bfed-e604e5127821\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/rest-api.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"9280e66609a7a65e141236963ad7b2533d22c543ba87cb1e924648644b79b5e4\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"dbe695be-edb1-4ffd-981f-895350db3efd\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"88e58ce61de9431c4ba5130f74f328e0011a3307975ac5cd532750bbc92893dc\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"17a3df9e-3a57-4e44-aca1-39087d4e16e9\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"39cf1ce4173aa61e04886b1f21c7128e0ee85b0b373e5bec93d0885389535fee\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"All storage backends are guaranteed to support key values up to 250 bytes in size. This field is required.  \\nvalue | `STRING` | String value of the tag being logged. Maximum size depends on storage backend. All storage backends are guaranteed to support key values up to 5000 bytes in size. This field is required.  \\n  \\n* * *\\n\\n## Delete Tag\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/runs/delete-tag` | `POST`  \\n  \\nDelete a tag on a run. Tags are run metadata that can be updated during a run\\nand after a run completes.\\n\\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nrun_id | `STRING` | ID of the run that the tag was logged under. Must be provided. This field is required.  \\nkey | `STRING` | Name of the tag. Maximum size is 255 bytes. Must be provided. This field is required.  \\n  \\n* * *\\n\\n## Log Param\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/runs/log-parameter` | `POST`  \\n  \\nLog a param used for a run. A param is a key-value pair (string key, string\\nvalue). Examples include hyperparameters used for ML model training and\\nconstant dates and values used in an ETL pipeline. A param can be logged only\\nonce for a run.\\n\\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nrun_id | `STRING` | ID of the run under which to log the param. Must be provided.  \\nrun_uuid | `STRING` | [Deprecated, use run_id instead] ID of the run under which to log the param. This field will be removed in a future MLflow version.  \\nkey | `STRING` | Name of the param. Maximum size is 255 bytes. This field is required.  \\nvalue | `STRING` | String value of the param being logged. Maximum size is 6000 bytes. This field is required.  \\n  \\n* * *\\n\\n## Get Metric History\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/metrics/get-history` | `GET`  \\n  \\nGet a list of all values for the specified metric for a given run.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 19389, \"end_char_idx\": 21241, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/rest-api.html","doc_id":"https://mlflow.org/docs/latest/rest-api.html","ref_doc_id":"https://mlflow.org/docs/latest/rest-api.html"}
{"_node_content":"{\"id_\": \"17a3df9e-3a57-4e44-aca1-39087d4e16e9\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/rest-api.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"9280e66609a7a65e141236963ad7b2533d22c543ba87cb1e924648644b79b5e4\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"01c59341-e34a-4e25-bfed-e604e5127821\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"d827e8043d06c6833dde2c2207906d99ffb32ba466ed3b3e4c7d37adf0941243\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"0df185a9-eaa8-4973-9943-d06f08a4cfb0\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"428cac12698c65c7b998f7ca38bc880c7c619a49a62ff538901458ab3bbb1182\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"A param can be logged only\\nonce for a run.\\n\\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nrun_id | `STRING` | ID of the run under which to log the param. Must be provided.  \\nrun_uuid | `STRING` | [Deprecated, use run_id instead] ID of the run under which to log the param. This field will be removed in a future MLflow version.  \\nkey | `STRING` | Name of the param. Maximum size is 255 bytes. This field is required.  \\nvalue | `STRING` | String value of the param being logged. Maximum size is 6000 bytes. This field is required.  \\n  \\n* * *\\n\\n## Get Metric History\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/metrics/get-history` | `GET`  \\n  \\nGet a list of all values for the specified metric for a given run.\\n\\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nrun_id | `STRING` | ID of the run from which to fetch metric values. Must be provided.  \\nrun_uuid | `STRING` | [Deprecated, use run_id instead] ID of the run from which to fetch metric values. This field will be removed in a future MLflow version.  \\nmetric_key | `STRING` | Name of the metric. This field is required.  \\npage_token | `STRING` | Token indicating the page of metric history to fetch  \\nmax_results | `INT32` | Maximum number of logged instances of a metric for a run to return per call. Backend servers may restrict the value of max_results depending on performance requirements. Requests that do not specify this value will behave as non-paginated queries where all metric history values for a given metric within a run are returned in a single response.  \\n  \\n### Response Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nmetrics | An array of Metric | All logged values for this metric.  \\nnext_page_token | `STRING` | Token that can be used to issue a query for the next page of metric history values. A missing token indicates that no additional metrics are available to fetch.  \\n  \\n* * *\\n\\n## Search Runs\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/runs/search` | `POST`  \\n  \\nSearch for runs that satisfy expressions. Search expressions can use Metric\\nand Param keys.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 20504, \"end_char_idx\": 22611, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/rest-api.html","doc_id":"https://mlflow.org/docs/latest/rest-api.html","ref_doc_id":"https://mlflow.org/docs/latest/rest-api.html"}
{"_node_content":"{\"id_\": \"0df185a9-eaa8-4973-9943-d06f08a4cfb0\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/rest-api.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"9280e66609a7a65e141236963ad7b2533d22c543ba87cb1e924648644b79b5e4\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"17a3df9e-3a57-4e44-aca1-39087d4e16e9\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"39cf1ce4173aa61e04886b1f21c7128e0ee85b0b373e5bec93d0885389535fee\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"7a4e63d0-8ee4-48c3-b165-34ebf6aa8764\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"2c56d70299559c87f825238d72a7dc98effadebca413c7bcd5fffb5b1f1a908c\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"Backend servers may restrict the value of max_results depending on performance requirements. Requests that do not specify this value will behave as non-paginated queries where all metric history values for a given metric within a run are returned in a single response.  \\n  \\n### Response Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nmetrics | An array of Metric | All logged values for this metric.  \\nnext_page_token | `STRING` | Token that can be used to issue a query for the next page of metric history values. A missing token indicates that no additional metrics are available to fetch.  \\n  \\n* * *\\n\\n## Search Runs\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/runs/search` | `POST`  \\n  \\nSearch for runs that satisfy expressions. Search expressions can use Metric\\nand Param keys.\\n\\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nexperiment_ids | An array of `STRING` | List of experiment IDs to search over.  \\nfilter | `STRING` | A filter expression over params, metrics, and tags, that allows returning a subset of runs. The syntax is a subset of SQL that supports ANDing together binary operations between a param, metric, or tag and a constant. Example: `metrics.rmse < 1 and params.model_class = 'LogisticRegression'` You can select columns with special characters (hyphen, space, period, etc.) by using double quotes: `metrics.\\\"model class\\\" = 'LinearRegression' and tags.\\\"user-name\\\" = 'Tomas'` Supported operators are `=`, `!=`, `>`, `>=`, `<`, and `<=`.  \\nrun_view_type | ViewType | Whether to display only active, only deleted, or all runs. Defaults to only active runs.  \\nmax_results | `INT32` | Maximum number of runs desired. If unspecified, defaults to 1000. All servers are guaranteed to support a max_results threshold of at least 50,000 but may support more. Callers of this endpoint are encouraged to pass max_results explicitly and leverage page_token to iterate through experiments.  \\norder_by | An array of `STRING` | List of columns to be ordered by, including attributes, params, metrics, and tags with an optional \\u00e2\\u0080\\u009cDESC\\u00e2\\u0080\\u009d or \\u00e2\\u0080\\u009cASC\\u00e2\\u0080\\u009d annotation, where \\u00e2\\u0080\\u009cASC\\u00e2\\u0080\\u009d is the default.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 21814, \"end_char_idx\": 23957, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/rest-api.html","doc_id":"https://mlflow.org/docs/latest/rest-api.html","ref_doc_id":"https://mlflow.org/docs/latest/rest-api.html"}
{"_node_content":"{\"id_\": \"7a4e63d0-8ee4-48c3-b165-34ebf6aa8764\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/rest-api.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"9280e66609a7a65e141236963ad7b2533d22c543ba87cb1e924648644b79b5e4\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"0df185a9-eaa8-4973-9943-d06f08a4cfb0\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"428cac12698c65c7b998f7ca38bc880c7c619a49a62ff538901458ab3bbb1182\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"106d7bb9-db28-4701-9cc8-7c2ca49a59fb\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"2a48b172909359cff6d4fc0f5c1e39213eff472cea59c9654d51fc9dda1b5d96\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"\\\"user-name\\\" = 'Tomas'` Supported operators are `=`, `!=`, `>`, `>=`, `<`, and `<=`.  \\nrun_view_type | ViewType | Whether to display only active, only deleted, or all runs. Defaults to only active runs.  \\nmax_results | `INT32` | Maximum number of runs desired. If unspecified, defaults to 1000. All servers are guaranteed to support a max_results threshold of at least 50,000 but may support more. Callers of this endpoint are encouraged to pass max_results explicitly and leverage page_token to iterate through experiments.  \\norder_by | An array of `STRING` | List of columns to be ordered by, including attributes, params, metrics, and tags with an optional \\u00e2\\u0080\\u009cDESC\\u00e2\\u0080\\u009d or \\u00e2\\u0080\\u009cASC\\u00e2\\u0080\\u009d annotation, where \\u00e2\\u0080\\u009cASC\\u00e2\\u0080\\u009d is the default. Example: [\\u00e2\\u0080\\u009cparams.input DESC\\u00e2\\u0080\\u009d, \\u00e2\\u0080\\u009cmetrics.alpha ASC\\u00e2\\u0080\\u009d, \\u00e2\\u0080\\u009cmetrics.rmse\\u00e2\\u0080\\u009d] Tiebreaks are done by start_time DESC followed by run_id for runs with the same start time (and this is the default ordering criterion if order_by is not provided).  \\npage_token | `STRING` |   \\n  \\n### Response Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nruns | An array of Run | Runs that match the search criteria.  \\nnext_page_token | `STRING` |   \\n  \\n* * *\\n\\n## List Artifacts\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/artifacts/list` | `GET`  \\n  \\nList artifacts for a run. Takes an optional `artifact_path` prefix which if\\nspecified, the response contains only artifacts with the specified prefix.\\n\\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nrun_id | `STRING` | ID of the run whose artifacts to list. Must be provided.  \\nrun_uuid | `STRING` | [Deprecated, use run_id instead] ID of the run whose artifacts to list. This field will be removed in a future MLflow version.  \\npath | `STRING` | Filter artifacts matching this path (a relative path from the root artifact directory).\", \"mimetype\": \"text/plain\", \"start_char_idx\": 23231, \"end_char_idx\": 25066, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/rest-api.html","doc_id":"https://mlflow.org/docs/latest/rest-api.html","ref_doc_id":"https://mlflow.org/docs/latest/rest-api.html"}
{"_node_content":"{\"id_\": \"106d7bb9-db28-4701-9cc8-7c2ca49a59fb\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/rest-api.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"9280e66609a7a65e141236963ad7b2533d22c543ba87cb1e924648644b79b5e4\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"7a4e63d0-8ee4-48c3-b165-34ebf6aa8764\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"2c56d70299559c87f825238d72a7dc98effadebca413c7bcd5fffb5b1f1a908c\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"41bf4835-f4cd-433a-acdf-c700672dd59c\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"f65dd6ee61c49dc06f370d33692f8a327c131235aa6946fcd14d3a6fb1947e93\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"next_page_token | `STRING` |   \\n  \\n* * *\\n\\n## List Artifacts\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/artifacts/list` | `GET`  \\n  \\nList artifacts for a run. Takes an optional `artifact_path` prefix which if\\nspecified, the response contains only artifacts with the specified prefix.\\n\\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nrun_id | `STRING` | ID of the run whose artifacts to list. Must be provided.  \\nrun_uuid | `STRING` | [Deprecated, use run_id instead] ID of the run whose artifacts to list. This field will be removed in a future MLflow version.  \\npath | `STRING` | Filter artifacts matching this path (a relative path from the root artifact directory).  \\npage_token | `STRING` | Token indicating the page of artifact results to fetch  \\n  \\n### Response Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nroot_uri | `STRING` | Root artifact directory for the run.  \\nfiles | An array of FileInfo | File location and metadata for artifacts.  \\nnext_page_token | `STRING` | Token that can be used to retrieve the next page of artifact results  \\n  \\n* * *\\n\\n## Update Run\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/runs/update` | `POST`  \\n  \\nUpdate run metadata.\\n\\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nrun_id | `STRING` | ID of the run to update. Must be provided.  \\nrun_uuid | `STRING` | [Deprecated, use run_id instead] ID of the run to update.. This field will be removed in a future MLflow version.  \\nstatus | RunStatus | Updated status of the run.  \\nend_time | `INT64` | Unix timestamp in milliseconds of when the run ended.  \\nrun_name | `STRING` | Updated name of the run.  \\n  \\n### Response Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nrun_info | RunInfo | Updated metadata of the run.  \\n  \\n* * *\\n\\n## Create RegisteredModel\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/registered-models/create` | `POST`  \\n  \\nThrows `RESOURCE_ALREADY_EXISTS` if a registered model with the given name\\nexists.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 24371, \"end_char_idx\": 26365, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/rest-api.html","doc_id":"https://mlflow.org/docs/latest/rest-api.html","ref_doc_id":"https://mlflow.org/docs/latest/rest-api.html"}
{"_node_content":"{\"id_\": \"41bf4835-f4cd-433a-acdf-c700672dd59c\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/rest-api.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"9280e66609a7a65e141236963ad7b2533d22c543ba87cb1e924648644b79b5e4\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"106d7bb9-db28-4701-9cc8-7c2ca49a59fb\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"2a48b172909359cff6d4fc0f5c1e39213eff472cea59c9654d51fc9dda1b5d96\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"c246f991-c8c5-44fa-a4b8-61051ee949f1\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"03959baf1a1a05a51e5548c90f67e4ba9444d11d1180a3f297d81aa0fdf782e1\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nrun_id | `STRING` | ID of the run to update. Must be provided.  \\nrun_uuid | `STRING` | [Deprecated, use run_id instead] ID of the run to update.. This field will be removed in a future MLflow version.  \\nstatus | RunStatus | Updated status of the run.  \\nend_time | `INT64` | Unix timestamp in milliseconds of when the run ended.  \\nrun_name | `STRING` | Updated name of the run.  \\n  \\n### Response Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nrun_info | RunInfo | Updated metadata of the run.  \\n  \\n* * *\\n\\n## Create RegisteredModel\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/registered-models/create` | `POST`  \\n  \\nThrows `RESOURCE_ALREADY_EXISTS` if a registered model with the given name\\nexists.\\n\\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nname | `STRING` | Register models under this name This field is required.  \\ntags | An array of RegisteredModelTag | Additional metadata for registered model.  \\ndescription | `STRING` | Optional description for registered model.  \\ndeployment_job_id | `STRING` | Deployment job id for this model.  \\n  \\n### Response Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nregistered_model | RegisteredModel |   \\n  \\n* * *\\n\\n## Get RegisteredModel\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/registered-models/get` | `GET`  \\n  \\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nname | `STRING` | Registered model unique name identifier. This field is required.  \\n  \\n### Response Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nregistered_model | RegisteredModel |   \\n  \\n* * *\\n\\n## Rename RegisteredModel\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/registered-models/rename` | `POST`  \\n  \\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nname | `STRING` | Registered model unique name identifier. This field is required.  \\nnew_name | `STRING` | If provided, updates the name for this `registered_model`.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 25582, \"end_char_idx\": 27598, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/rest-api.html","doc_id":"https://mlflow.org/docs/latest/rest-api.html","ref_doc_id":"https://mlflow.org/docs/latest/rest-api.html"}
{"_node_content":"{\"id_\": \"c246f991-c8c5-44fa-a4b8-61051ee949f1\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/rest-api.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"9280e66609a7a65e141236963ad7b2533d22c543ba87cb1e924648644b79b5e4\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"41bf4835-f4cd-433a-acdf-c700672dd59c\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"f65dd6ee61c49dc06f370d33692f8a327c131235aa6946fcd14d3a6fb1947e93\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"e0a93bab-fe12-4f6b-8674-7e3cf94f9417\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"8919f909c4c37f3668568a36ae0a6c5bd95501c9f1ed782cf563210a1e27ce38\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"This field is required.  \\n  \\n### Response Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nregistered_model | RegisteredModel |   \\n  \\n* * *\\n\\n## Rename RegisteredModel\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/registered-models/rename` | `POST`  \\n  \\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nname | `STRING` | Registered model unique name identifier. This field is required.  \\nnew_name | `STRING` | If provided, updates the name for this `registered_model`.  \\n  \\n### Response Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nregistered_model | RegisteredModel |   \\n  \\n* * *\\n\\n## Update RegisteredModel\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/registered-models/update` | `PATCH`  \\n  \\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nname | `STRING` | Registered model unique name identifier. This field is required.  \\ndescription | `STRING` | If provided, updates the description for this `registered_model`.  \\ndeployment_job_id | `STRING` | Deployment job id for this model.  \\n  \\n### Response Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nregistered_model | RegisteredModel |   \\n  \\n* * *\\n\\n## Delete RegisteredModel\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/registered-models/delete` | `DELETE`  \\n  \\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nname | `STRING` | Registered model unique name identifier. This field is required.  \\n  \\n* * *\\n\\n## Get Latest ModelVersions\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/registered-models/get-latest-versions` | `POST`  \\n  \\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nname | `STRING` | Registered model unique name identifier. This field is required.  \\nstages | An array of `STRING` | List of stages.  \\n  \\n### Response Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nmodel_versions | An array of ModelVersion | Latest version models for each requests stage. Only return models with current `READY` status. If no `stages` provided, returns the latest version for each stage, including `\\\"None\\\"`.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 27097, \"end_char_idx\": 29200, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/rest-api.html","doc_id":"https://mlflow.org/docs/latest/rest-api.html","ref_doc_id":"https://mlflow.org/docs/latest/rest-api.html"}
{"_node_content":"{\"id_\": \"e0a93bab-fe12-4f6b-8674-7e3cf94f9417\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/rest-api.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"9280e66609a7a65e141236963ad7b2533d22c543ba87cb1e924648644b79b5e4\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"c246f991-c8c5-44fa-a4b8-61051ee949f1\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"03959baf1a1a05a51e5548c90f67e4ba9444d11d1180a3f297d81aa0fdf782e1\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"7fc6d752-eaa9-4b7b-b3a8-2cb1c2d370dd\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"8a50befb5277e4fc5427217388bde5bf42fb0a7ac3f594c1596fcdd242c4d24b\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"This field is required.  \\n  \\n* * *\\n\\n## Get Latest ModelVersions\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/registered-models/get-latest-versions` | `POST`  \\n  \\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nname | `STRING` | Registered model unique name identifier. This field is required.  \\nstages | An array of `STRING` | List of stages.  \\n  \\n### Response Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nmodel_versions | An array of ModelVersion | Latest version models for each requests stage. Only return models with current `READY` status. If no `stages` provided, returns the latest version for each stage, including `\\\"None\\\"`.  \\n  \\n* * *\\n\\n## Create ModelVersion\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/model-versions/create` | `POST`  \\n  \\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nname | `STRING` | Register model under this name This field is required.  \\nsource | `STRING` | URI indicating the location of the model artifacts. This field is required.  \\nrun_id | `STRING` | MLflow run ID for correlation, if `source` was generated by an experiment run in MLflow tracking server  \\ntags | An array of ModelVersionTag | Additional metadata for model version.  \\nrun_link | `STRING` | MLflow run link - this is the exact link of the run that generated this model version, potentially hosted at another instance of MLflow.  \\ndescription | `STRING` | Optional description for model version.  \\nmodel_id | `STRING` | Optional model_id for model version that is used to link the registered model to the source logged model  \\n  \\n### Response Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nmodel_version | ModelVersion | Return new version number generated for this model in registry.  \\n  \\n* * *\\n\\n## Get ModelVersion\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/model-versions/get` | `GET`  \\n  \\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nname | `STRING` | Name of the registered model This field is required.  \\nversion | `STRING` | Model version number This field is required.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 28528, \"end_char_idx\": 30618, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/rest-api.html","doc_id":"https://mlflow.org/docs/latest/rest-api.html","ref_doc_id":"https://mlflow.org/docs/latest/rest-api.html"}
{"_node_content":"{\"id_\": \"7fc6d752-eaa9-4b7b-b3a8-2cb1c2d370dd\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/rest-api.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"9280e66609a7a65e141236963ad7b2533d22c543ba87cb1e924648644b79b5e4\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"e0a93bab-fe12-4f6b-8674-7e3cf94f9417\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"8919f909c4c37f3668568a36ae0a6c5bd95501c9f1ed782cf563210a1e27ce38\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"6427f21e-69f3-4a33-bc83-1402e2c9163d\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"1a2778f27adea6a6f7c0be004632b2aaa5eb827bdf48b3dcd086cb5fbdbebd69\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"run_link | `STRING` | MLflow run link - this is the exact link of the run that generated this model version, potentially hosted at another instance of MLflow.  \\ndescription | `STRING` | Optional description for model version.  \\nmodel_id | `STRING` | Optional model_id for model version that is used to link the registered model to the source logged model  \\n  \\n### Response Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nmodel_version | ModelVersion | Return new version number generated for this model in registry.  \\n  \\n* * *\\n\\n## Get ModelVersion\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/model-versions/get` | `GET`  \\n  \\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nname | `STRING` | Name of the registered model This field is required.  \\nversion | `STRING` | Model version number This field is required.  \\n  \\n### Response Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nmodel_version | ModelVersion |   \\n  \\n* * *\\n\\n## Update ModelVersion\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/model-versions/update` | `PATCH`  \\n  \\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nname | `STRING` | Name of the registered model This field is required.  \\nversion | `STRING` | Model version number This field is required.  \\ndescription | `STRING` | If provided, updates the description for this `registered_model`.  \\n  \\n### Response Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nmodel_version | ModelVersion | Return new version number generated for this model in registry.  \\n  \\n* * *\\n\\n## Delete ModelVersion\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/model-versions/delete` | `DELETE`  \\n  \\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nname | `STRING` | Name of the registered model This field is required.  \\nversion | `STRING` | Model version number This field is required.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 29769, \"end_char_idx\": 31655, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/rest-api.html","doc_id":"https://mlflow.org/docs/latest/rest-api.html","ref_doc_id":"https://mlflow.org/docs/latest/rest-api.html"}
{"_node_content":"{\"id_\": \"6427f21e-69f3-4a33-bc83-1402e2c9163d\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/rest-api.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"9280e66609a7a65e141236963ad7b2533d22c543ba87cb1e924648644b79b5e4\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"7fc6d752-eaa9-4b7b-b3a8-2cb1c2d370dd\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"8a50befb5277e4fc5427217388bde5bf42fb0a7ac3f594c1596fcdd242c4d24b\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"b6f06e9d-19f2-45df-902c-419388b694c2\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"9ac7d122607f9ff194f53b5b7a0fe7141654b4a140ff1b5081c3d3572f0538bf\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"version | `STRING` | Model version number This field is required.  \\ndescription | `STRING` | If provided, updates the description for this `registered_model`.  \\n  \\n### Response Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nmodel_version | ModelVersion | Return new version number generated for this model in registry.  \\n  \\n* * *\\n\\n## Delete ModelVersion\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/model-versions/delete` | `DELETE`  \\n  \\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nname | `STRING` | Name of the registered model This field is required.  \\nversion | `STRING` | Model version number This field is required.  \\n  \\n* * *\\n\\n## Search ModelVersions\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/model-versions/search` | `GET`  \\n  \\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nfilter | `STRING` | String filter condition, like \\u00e2\\u0080\\u009cname=\\u00e2\\u0080\\u0099my-model-name\\u00e2\\u0080\\u0099\\u00e2\\u0080\\u009d. Must be a single boolean condition, with string values wrapped in single quotes.  \\nmax_results | `INT64` | Maximum number of models desired. Max threshold is 200K. Backends may choose a lower default value and maximum threshold.  \\norder_by | An array of `STRING` | List of columns to be ordered by including model name, version, stage with an optional \\u00e2\\u0080\\u009cDESC\\u00e2\\u0080\\u009d or \\u00e2\\u0080\\u009cASC\\u00e2\\u0080\\u009d annotation, where \\u00e2\\u0080\\u009cASC\\u00e2\\u0080\\u009d is the default. Tiebreaks are done by latest stage transition timestamp, followed by name ASC, followed by version DESC.  \\npage_token | `STRING` | Pagination token to go to next page based on previous search query.  \\n  \\n### Response Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nmodel_versions | An array of ModelVersion | Models that match the search criteria  \\nnext_page_token | `STRING` | Pagination token to request next page of models for the same search query.  \\n  \\n* * *\\n\\n## Get Download URI For ModelVersion Artifacts\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/model-versions/get-download-uri` | `GET`  \\n  \\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nname | `STRING` | Name of the registered model This field is required.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 30993, \"end_char_idx\": 33107, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/rest-api.html","doc_id":"https://mlflow.org/docs/latest/rest-api.html","ref_doc_id":"https://mlflow.org/docs/latest/rest-api.html"}
{"_node_content":"{\"id_\": \"b6f06e9d-19f2-45df-902c-419388b694c2\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/rest-api.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"9280e66609a7a65e141236963ad7b2533d22c543ba87cb1e924648644b79b5e4\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"6427f21e-69f3-4a33-bc83-1402e2c9163d\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"1a2778f27adea6a6f7c0be004632b2aaa5eb827bdf48b3dcd086cb5fbdbebd69\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"7d63dd76-3514-452a-8514-b2a9bf663a9b\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"92caabddd249aeb2892a91c25c9d11241cdadb923b02b61ac05428e18351f2ae\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"Tiebreaks are done by latest stage transition timestamp, followed by name ASC, followed by version DESC.  \\npage_token | `STRING` | Pagination token to go to next page based on previous search query.  \\n  \\n### Response Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nmodel_versions | An array of ModelVersion | Models that match the search criteria  \\nnext_page_token | `STRING` | Pagination token to request next page of models for the same search query.  \\n  \\n* * *\\n\\n## Get Download URI For ModelVersion Artifacts\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/model-versions/get-download-uri` | `GET`  \\n  \\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nname | `STRING` | Name of the registered model This field is required.  \\nversion | `STRING` | Model version number This field is required.  \\n  \\n### Response Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nartifact_uri | `STRING` | URI corresponding to where artifacts for this model version are stored.  \\n  \\n* * *\\n\\n## Transition ModelVersion Stage\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/model-versions/transition-stage` | `POST`  \\n  \\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nname | `STRING` | Name of the registered model This field is required.  \\nversion | `STRING` | Model version number This field is required.  \\nstage | `STRING` | Transition model_version to new stage. This field is required.  \\narchive_existing_versions | `BOOL` | When transitioning a model version to a particular stage, this flag dictates whether all existing model versions in that stage should be atomically moved to the \\u00e2\\u0080\\u009carchived\\u00e2\\u0080\\u009d stage. This ensures that at-most-one model version exists in the target stage. This field is _required_ when transitioning a model versions\\u00e2\\u0080\\u0099s stage This field is required.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 32349, \"end_char_idx\": 34173, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/rest-api.html","doc_id":"https://mlflow.org/docs/latest/rest-api.html","ref_doc_id":"https://mlflow.org/docs/latest/rest-api.html"}
{"_node_content":"{\"id_\": \"7d63dd76-3514-452a-8514-b2a9bf663a9b\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/rest-api.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"9280e66609a7a65e141236963ad7b2533d22c543ba87cb1e924648644b79b5e4\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"b6f06e9d-19f2-45df-902c-419388b694c2\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"9ac7d122607f9ff194f53b5b7a0fe7141654b4a140ff1b5081c3d3572f0538bf\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"d4ce602b-b232-4af4-9551-b8b56d6e8ece\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"379b9c573d1bd13db988d89276c68480caa4bd11036eb239e908336bc6b2bc47\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"* * *\\n\\n## Transition ModelVersion Stage\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/model-versions/transition-stage` | `POST`  \\n  \\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nname | `STRING` | Name of the registered model This field is required.  \\nversion | `STRING` | Model version number This field is required.  \\nstage | `STRING` | Transition model_version to new stage. This field is required.  \\narchive_existing_versions | `BOOL` | When transitioning a model version to a particular stage, this flag dictates whether all existing model versions in that stage should be atomically moved to the \\u00e2\\u0080\\u009carchived\\u00e2\\u0080\\u009d stage. This ensures that at-most-one model version exists in the target stage. This field is _required_ when transitioning a model versions\\u00e2\\u0080\\u0099s stage This field is required.  \\n  \\n### Response Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nmodel_version | ModelVersion | Updated model version  \\n  \\n* * *\\n\\n## Search RegisteredModels\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/registered-models/search` | `GET`  \\n  \\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nfilter | `STRING` | String filter condition, like \\u00e2\\u0080\\u009cname LIKE \\u00e2\\u0080\\u0098my-model-name\\u00e2\\u0080\\u0099\\u00e2\\u0080\\u009d. Interpreted in the backend automatically as \\u00e2\\u0080\\u009cname LIKE \\u00e2\\u0080\\u0098%my-model-name%\\u00e2\\u0080\\u0099\\u00e2\\u0080\\u009d. Single boolean condition, with string values wrapped in single quotes.  \\nmax_results | `INT64` | Maximum number of models desired. Default is 100. Max threshold is 1000.  \\norder_by | An array of `STRING` | List of columns for ordering search results, which can include model name and last updated timestamp with an optional \\u00e2\\u0080\\u009cDESC\\u00e2\\u0080\\u009d or \\u00e2\\u0080\\u009cASC\\u00e2\\u0080\\u009d annotation, where \\u00e2\\u0080\\u009cASC\\u00e2\\u0080\\u009d is the default. Tiebreaks are done by model name ASC.  \\npage_token | `STRING` | Pagination token to go to the next page based on a previous search query.  \\n  \\n### Response Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nregistered_models | An array of RegisteredModel | Registered Models that match the search criteria.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 33356, \"end_char_idx\": 35377, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/rest-api.html","doc_id":"https://mlflow.org/docs/latest/rest-api.html","ref_doc_id":"https://mlflow.org/docs/latest/rest-api.html"}
{"_node_content":"{\"id_\": \"d4ce602b-b232-4af4-9551-b8b56d6e8ece\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/rest-api.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"9280e66609a7a65e141236963ad7b2533d22c543ba87cb1e924648644b79b5e4\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"7d63dd76-3514-452a-8514-b2a9bf663a9b\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"92caabddd249aeb2892a91c25c9d11241cdadb923b02b61ac05428e18351f2ae\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"fb83c3e8-c522-4ab3-baa7-87dc7f129d63\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"81ec4c2839ad845b0bb99e1d368ad102578e2a71f6e21fb463bfee3d13614c6a\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"Interpreted in the backend automatically as \\u00e2\\u0080\\u009cname LIKE \\u00e2\\u0080\\u0098%my-model-name%\\u00e2\\u0080\\u0099\\u00e2\\u0080\\u009d. Single boolean condition, with string values wrapped in single quotes.  \\nmax_results | `INT64` | Maximum number of models desired. Default is 100. Max threshold is 1000.  \\norder_by | An array of `STRING` | List of columns for ordering search results, which can include model name and last updated timestamp with an optional \\u00e2\\u0080\\u009cDESC\\u00e2\\u0080\\u009d or \\u00e2\\u0080\\u009cASC\\u00e2\\u0080\\u009d annotation, where \\u00e2\\u0080\\u009cASC\\u00e2\\u0080\\u009d is the default. Tiebreaks are done by model name ASC.  \\npage_token | `STRING` | Pagination token to go to the next page based on a previous search query.  \\n  \\n### Response Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nregistered_models | An array of RegisteredModel | Registered Models that match the search criteria.  \\nnext_page_token | `STRING` | Pagination token to request the next page of models.  \\n  \\n* * *\\n\\n## Set Registered Model Tag\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/registered-models/set-tag` | `POST`  \\n  \\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nname | `STRING` | Unique name of the model. This field is required.  \\nkey | `STRING` | Name of the tag. Maximum size depends on storage backend. If a tag with this name already exists, its preexisting value will be replaced by the specified value. All storage backends are guaranteed to support key values up to 250 bytes in size. This field is required.  \\nvalue | `STRING` | String value of the tag being logged. Maximum size depends on storage backend. This field is required.  \\n  \\n* * *\\n\\n## Set Model Version Tag\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/model-versions/set-tag` | `POST`  \\n  \\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nname | `STRING` | Unique name of the model. This field is required.  \\nversion | `STRING` | Model version number. This field is required.  \\nkey | `STRING` | Name of the tag. Maximum size depends on storage backend.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 34588, \"end_char_idx\": 36548, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/rest-api.html","doc_id":"https://mlflow.org/docs/latest/rest-api.html","ref_doc_id":"https://mlflow.org/docs/latest/rest-api.html"}
{"_node_content":"{\"id_\": \"fb83c3e8-c522-4ab3-baa7-87dc7f129d63\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/rest-api.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"9280e66609a7a65e141236963ad7b2533d22c543ba87cb1e924648644b79b5e4\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"d4ce602b-b232-4af4-9551-b8b56d6e8ece\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"379b9c573d1bd13db988d89276c68480caa4bd11036eb239e908336bc6b2bc47\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"7d72da8b-edc0-43ff-9375-d3e76a0fd950\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"bf2787174fadc7954336c63d0c067c1c288409b8c633754f62660a570375c4fa\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"Maximum size depends on storage backend. If a tag with this name already exists, its preexisting value will be replaced by the specified value. All storage backends are guaranteed to support key values up to 250 bytes in size. This field is required.  \\nvalue | `STRING` | String value of the tag being logged. Maximum size depends on storage backend. This field is required.  \\n  \\n* * *\\n\\n## Set Model Version Tag\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/model-versions/set-tag` | `POST`  \\n  \\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nname | `STRING` | Unique name of the model. This field is required.  \\nversion | `STRING` | Model version number. This field is required.  \\nkey | `STRING` | Name of the tag. Maximum size depends on storage backend. If a tag with this name already exists, its preexisting value will be replaced by the specified value. All storage backends are guaranteed to support key values up to 250 bytes in size. This field is required.  \\nvalue | `STRING` | String value of the tag being logged. Maximum size depends on storage backend. This field is required.  \\n  \\n* * *\\n\\n## Delete Registered Model Tag\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/registered-models/delete-tag` | `DELETE`  \\n  \\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nname | `STRING` | Name of the registered model that the tag was logged under. This field is required.  \\nkey | `STRING` | Name of the tag. The name must be an exact match; wild-card deletion is not supported. Maximum size is 250 bytes. This field is required.  \\n  \\n* * *\\n\\n## Delete Model Version Tag\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/model-versions/delete-tag` | `DELETE`  \\n  \\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nname | `STRING` | Name of the registered model that the tag was logged under. This field is required.  \\nversion | `STRING` | Model version number that the tag was logged under. This field is required.  \\nkey | `STRING` | Name of the tag.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 35766, \"end_char_idx\": 37788, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/rest-api.html","doc_id":"https://mlflow.org/docs/latest/rest-api.html","ref_doc_id":"https://mlflow.org/docs/latest/rest-api.html"}
{"_node_content":"{\"id_\": \"7d72da8b-edc0-43ff-9375-d3e76a0fd950\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/rest-api.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"9280e66609a7a65e141236963ad7b2533d22c543ba87cb1e924648644b79b5e4\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"fb83c3e8-c522-4ab3-baa7-87dc7f129d63\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"81ec4c2839ad845b0bb99e1d368ad102578e2a71f6e21fb463bfee3d13614c6a\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"38424634-0038-4a6a-aa02-28426e20be0c\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"b5c52254bd5f70e7287163aeb47e2a07fa8000c9470c68b1959ccac0b06a94ed\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"This field is required.  \\nkey | `STRING` | Name of the tag. The name must be an exact match; wild-card deletion is not supported. Maximum size is 250 bytes. This field is required.  \\n  \\n* * *\\n\\n## Delete Model Version Tag\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/model-versions/delete-tag` | `DELETE`  \\n  \\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nname | `STRING` | Name of the registered model that the tag was logged under. This field is required.  \\nversion | `STRING` | Model version number that the tag was logged under. This field is required.  \\nkey | `STRING` | Name of the tag. The name must be an exact match; wild-card deletion is not supported. Maximum size is 250 bytes. This field is required.  \\n  \\n* * *\\n\\n## Delete Registered Model Alias\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/registered-models/alias` | `DELETE`  \\n  \\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nname | `STRING` | Name of the registered model. This field is required.  \\nalias | `STRING` | Name of the alias. The name must be an exact match; wild-card deletion is not supported. Maximum size is 256 bytes. This field is required.  \\n  \\n* * *\\n\\n## Get Model Version by Alias\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/registered-models/alias` | `GET`  \\n  \\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nname | `STRING` | Name of the registered model. This field is required.  \\nalias | `STRING` | Name of the alias. Maximum size is 256 bytes. This field is required.  \\n  \\n### Response Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nmodel_version | ModelVersion |   \\n  \\n* * *\\n\\n## Set Registered Model Alias\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/registered-models/alias` | `POST`  \\n  \\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nname | `STRING` | Name of the registered model. This field is required.  \\nalias | `STRING` | Name of the alias.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 37169, \"end_char_idx\": 39130, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/rest-api.html","doc_id":"https://mlflow.org/docs/latest/rest-api.html","ref_doc_id":"https://mlflow.org/docs/latest/rest-api.html"}
{"_node_content":"{\"id_\": \"38424634-0038-4a6a-aa02-28426e20be0c\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/rest-api.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"9280e66609a7a65e141236963ad7b2533d22c543ba87cb1e924648644b79b5e4\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"7d72da8b-edc0-43ff-9375-d3e76a0fd950\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"bf2787174fadc7954336c63d0c067c1c288409b8c633754f62660a570375c4fa\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"8a4d1193-fcb2-45e6-bbbb-0777c0de464c\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"6b961d2778cfd58f1211aa266bb2b4025d02899aba3216118308b6be92017374\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"This field is required.  \\nalias | `STRING` | Name of the alias. Maximum size is 256 bytes. This field is required.  \\n  \\n### Response Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nmodel_version | ModelVersion |   \\n  \\n* * *\\n\\n## Set Registered Model Alias\\n\\nEndpoint | HTTP Method  \\n---|---  \\n`2.0/mlflow/registered-models/alias` | `POST`  \\n  \\n### Request Structure\\n\\nField Name | Type | Description  \\n---|---|---  \\nname | `STRING` | Name of the registered model. This field is required.  \\nalias | `STRING` | Name of the alias. Maximum size depends on storage backend. If an alias with this name already exists, its preexisting value will be replaced by the specified version. All storage backends are guaranteed to support alias name values up to 256 bytes in size. This field is required.  \\nversion | `STRING` | Model version number. This field is required.  \\n  \\n## Data Structures\\n\\n### Dataset\\n\\nDataset. Represents a reference to data used for training, testing, or\\nevaluation during the model development process.\\n\\nField Name | Type | Description  \\n---|---|---  \\nname | `STRING` | The name of the dataset. E.g. ?my.uc.table@2? ?nyc-taxi-dataset?, ?fantastic-elk-3? This field is required.  \\ndigest | `STRING` | Dataset digest, e.g. an md5 hash of the dataset that uniquely identifies it within datasets of the same name. This field is required.  \\nsource_type | `STRING` | The type of the dataset source, e.g. ?databricks-uc-table?, ?DBFS?, ?S3?, \\u00e2\\u0080\\u00a6 This field is required.  \\nsource | `STRING` | Source information for the dataset. Note that the source may not exactly reproduce the dataset if it was transformed / modified before use with MLflow. This field is required.  \\nschema | `STRING` | The schema of the dataset. E.g., MLflow ColSpec JSON for a dataframe, MLflow TensorSpec JSON for an ndarray, or another schema format.  \\nprofile | `STRING` | The profile of the dataset.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 38595, \"end_char_idx\": 40486, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/rest-api.html","doc_id":"https://mlflow.org/docs/latest/rest-api.html","ref_doc_id":"https://mlflow.org/docs/latest/rest-api.html"}
{"_node_content":"{\"id_\": \"8a4d1193-fcb2-45e6-bbbb-0777c0de464c\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/rest-api.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"9280e66609a7a65e141236963ad7b2533d22c543ba87cb1e924648644b79b5e4\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"38424634-0038-4a6a-aa02-28426e20be0c\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"b5c52254bd5f70e7287163aeb47e2a07fa8000c9470c68b1959ccac0b06a94ed\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"d35774cc-bdea-4c07-829e-4200b659d03a\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"a7c3c334fbc0e0a6d9fd16815be4c0cf44f167a3c3f1c7d7699c87daffbab295\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"?nyc-taxi-dataset?, ?fantastic-elk-3? This field is required.  \\ndigest | `STRING` | Dataset digest, e.g. an md5 hash of the dataset that uniquely identifies it within datasets of the same name. This field is required.  \\nsource_type | `STRING` | The type of the dataset source, e.g. ?databricks-uc-table?, ?DBFS?, ?S3?, \\u00e2\\u0080\\u00a6 This field is required.  \\nsource | `STRING` | Source information for the dataset. Note that the source may not exactly reproduce the dataset if it was transformed / modified before use with MLflow. This field is required.  \\nschema | `STRING` | The schema of the dataset. E.g., MLflow ColSpec JSON for a dataframe, MLflow TensorSpec JSON for an ndarray, or another schema format.  \\nprofile | `STRING` | The profile of the dataset. Summary statistics for the dataset, such as the number of rows in a table, the mean / std / mode of each column in a table, or the number of elements in an array.  \\n  \\n### DatasetInput\\n\\nDatasetInput. Represents a dataset and input tags.\\n\\nField Name | Type | Description  \\n---|---|---  \\ntags | An array of InputTag | A list of tags for the dataset input, e.g. a ?context? tag with value ?training?  \\ndataset | Dataset | The dataset being used as a Run input. This field is required.  \\n  \\n### Experiment\\n\\nExperiment\\n\\nField Name | Type | Description  \\n---|---|---  \\nexperiment_id | `STRING` | Unique identifier for the experiment.  \\nname | `STRING` | Human readable name that identifies the experiment.  \\nartifact_location | `STRING` | Location where artifacts for the experiment are stored.  \\nlifecycle_stage | `STRING` | Current life cycle stage of the experiment: \\u00e2\\u0080\\u009cactive\\u00e2\\u0080\\u009d or \\u00e2\\u0080\\u009cdeleted\\u00e2\\u0080\\u009d. Deleted experiments are not returned by APIs.  \\nlast_update_time | `INT64` | Last update time  \\ncreation_time | `INT64` | Creation time  \\ntags | An array of ExperimentTag | Tags: Additional metadata key-value pairs.  \\n  \\n### ExperimentTag\\n\\nTag for an experiment.\\n\\nField Name | Type | Description  \\n---|---|---  \\nkey | `STRING` | The tag key.  \\nvalue | `STRING` | The tag value.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 39734, \"end_char_idx\": 41759, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/rest-api.html","doc_id":"https://mlflow.org/docs/latest/rest-api.html","ref_doc_id":"https://mlflow.org/docs/latest/rest-api.html"}
{"_node_content":"{\"id_\": \"d35774cc-bdea-4c07-829e-4200b659d03a\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/rest-api.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"9280e66609a7a65e141236963ad7b2533d22c543ba87cb1e924648644b79b5e4\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"8a4d1193-fcb2-45e6-bbbb-0777c0de464c\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"6b961d2778cfd58f1211aa266bb2b4025d02899aba3216118308b6be92017374\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"9c2c866d-9609-4278-b98a-d67609f510cd\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"6db5d9c37dfd4d3a140cb7e904cf513551dcfaa336eb2c9171413f6fc93de9c9\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"This field is required.  \\n  \\n### Experiment\\n\\nExperiment\\n\\nField Name | Type | Description  \\n---|---|---  \\nexperiment_id | `STRING` | Unique identifier for the experiment.  \\nname | `STRING` | Human readable name that identifies the experiment.  \\nartifact_location | `STRING` | Location where artifacts for the experiment are stored.  \\nlifecycle_stage | `STRING` | Current life cycle stage of the experiment: \\u00e2\\u0080\\u009cactive\\u00e2\\u0080\\u009d or \\u00e2\\u0080\\u009cdeleted\\u00e2\\u0080\\u009d. Deleted experiments are not returned by APIs.  \\nlast_update_time | `INT64` | Last update time  \\ncreation_time | `INT64` | Creation time  \\ntags | An array of ExperimentTag | Tags: Additional metadata key-value pairs.  \\n  \\n### ExperimentTag\\n\\nTag for an experiment.\\n\\nField Name | Type | Description  \\n---|---|---  \\nkey | `STRING` | The tag key.  \\nvalue | `STRING` | The tag value.  \\n  \\n### FileInfo\\n\\nField Name | Type | Description  \\n---|---|---  \\npath | `STRING` | Path relative to the root artifact directory run.  \\nis_dir | `BOOL` | Whether the path is a directory.  \\nfile_size | `INT64` | Size in bytes. Unset for directories.  \\n  \\n### InputTag\\n\\nTag for an input.\\n\\nField Name | Type | Description  \\n---|---|---  \\nkey | `STRING` | The tag key. This field is required.  \\nvalue | `STRING` | The tag value. This field is required.  \\n  \\n### Metric\\n\\nMetric associated with a run, represented as a key-value pair.\\n\\nField Name | Type | Description  \\n---|---|---  \\nkey | `STRING` | Key identifying this metric.  \\nvalue | `DOUBLE` | Value associated with this metric.  \\ntimestamp | `INT64` | The timestamp at which this metric was recorded.  \\nstep | `INT64` | Step at which to log the metric.  \\n  \\n### ModelInput\\n\\nRepresents a LoggedModel or Registered Model Version input to a Run.\\n\\nField Name | Type | Description  \\n---|---|---  \\nmodel_id | `STRING` | The unique identifier of the model. This field is required.  \\n  \\n### ModelMetric\\n\\nMetric associated with a model, represented as a key-value pair. Copied from\\nMLflow metric\\n\\nField Name | Type | Description  \\n---|---|---  \\nkey | `STRING` | Key identifying this metric.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 40945, \"end_char_idx\": 42994, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/rest-api.html","doc_id":"https://mlflow.org/docs/latest/rest-api.html","ref_doc_id":"https://mlflow.org/docs/latest/rest-api.html"}
{"_node_content":"{\"id_\": \"9c2c866d-9609-4278-b98a-d67609f510cd\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/rest-api.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"9280e66609a7a65e141236963ad7b2533d22c543ba87cb1e924648644b79b5e4\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"d35774cc-bdea-4c07-829e-4200b659d03a\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"a7c3c334fbc0e0a6d9fd16815be4c0cf44f167a3c3f1c7d7699c87daffbab295\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"d0ca33b4-f348-4cb2-ad52-1ff0a75dc606\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"99d28c2a9390e7966279e213f874865ce9bc84bf2aea3192d512198d60ec9587\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"This field is required.  \\n  \\n### Metric\\n\\nMetric associated with a run, represented as a key-value pair.\\n\\nField Name | Type | Description  \\n---|---|---  \\nkey | `STRING` | Key identifying this metric.  \\nvalue | `DOUBLE` | Value associated with this metric.  \\ntimestamp | `INT64` | The timestamp at which this metric was recorded.  \\nstep | `INT64` | Step at which to log the metric.  \\n  \\n### ModelInput\\n\\nRepresents a LoggedModel or Registered Model Version input to a Run.\\n\\nField Name | Type | Description  \\n---|---|---  \\nmodel_id | `STRING` | The unique identifier of the model. This field is required.  \\n  \\n### ModelMetric\\n\\nMetric associated with a model, represented as a key-value pair. Copied from\\nMLflow metric\\n\\nField Name | Type | Description  \\n---|---|---  \\nkey | `STRING` | Key identifying this metric.  \\nvalue | `DOUBLE` | Value associated with this metric.  \\ntimestamp | `INT64` | The timestamp at which this metric was recorded.  \\nstep | `INT64` | Step at which to log the metric.  \\n  \\n### ModelOutput\\n\\nRepresents a LoggedModel output of a Run.\\n\\nField Name | Type | Description  \\n---|---|---  \\nmodel_id | `STRING` | The unique identifier of the model. This field is required.  \\nstep | `INT64` | Step at which the model was produced. This field is required.  \\n  \\n### ModelParam\\n\\nParam for a model version.\\n\\nField Name | Type | Description  \\n---|---|---  \\nname | `STRING` | Name of the param.  \\nvalue | `STRING` | Value of the param associated with the name, could be empty  \\n  \\n### ModelVersion\\n\\nField Name | Type | Description  \\n---|---|---  \\nname | `STRING` | Unique name of the model  \\nversion | `STRING` | Model\\u00e2\\u0080\\u0099s version number.  \\ncreation_timestamp | `INT64` | Timestamp recorded when this `model_version` was created.  \\nlast_updated_timestamp | `INT64` | Timestamp recorded when metadata for this `model_version` was last updated.  \\nuser_id | `STRING` | User that created this `model_version`.  \\ncurrent_stage | `STRING` | Current stage for this `model_version`.  \\ndescription | `STRING` | Description of this `model_version`.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 42186, \"end_char_idx\": 44229, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/rest-api.html","doc_id":"https://mlflow.org/docs/latest/rest-api.html","ref_doc_id":"https://mlflow.org/docs/latest/rest-api.html"}
{"_node_content":"{\"id_\": \"d0ca33b4-f348-4cb2-ad52-1ff0a75dc606\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/rest-api.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"9280e66609a7a65e141236963ad7b2533d22c543ba87cb1e924648644b79b5e4\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"9c2c866d-9609-4278-b98a-d67609f510cd\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"6db5d9c37dfd4d3a140cb7e904cf513551dcfaa336eb2c9171413f6fc93de9c9\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"718c6ba5-e189-45ca-8fbf-4bc760658f9e\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"7bb2e29128b0dd6c3cbb984fba7aea1c77a4a7c758c2b5b7d2c90d966aaa2f98\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"This field is required.  \\n  \\n### ModelParam\\n\\nParam for a model version.\\n\\nField Name | Type | Description  \\n---|---|---  \\nname | `STRING` | Name of the param.  \\nvalue | `STRING` | Value of the param associated with the name, could be empty  \\n  \\n### ModelVersion\\n\\nField Name | Type | Description  \\n---|---|---  \\nname | `STRING` | Unique name of the model  \\nversion | `STRING` | Model\\u00e2\\u0080\\u0099s version number.  \\ncreation_timestamp | `INT64` | Timestamp recorded when this `model_version` was created.  \\nlast_updated_timestamp | `INT64` | Timestamp recorded when metadata for this `model_version` was last updated.  \\nuser_id | `STRING` | User that created this `model_version`.  \\ncurrent_stage | `STRING` | Current stage for this `model_version`.  \\ndescription | `STRING` | Description of this `model_version`.  \\nsource | `STRING` | URI indicating the location of the source model artifacts, used when creating `model_version`  \\nrun_id | `STRING` | MLflow run ID used when creating `model_version`, if `source` was generated by an experiment run stored in MLflow tracking server.  \\nstatus | ModelVersionStatus | Current status of `model_version`  \\nstatus_message | `STRING` | Details on current `status`, if it is pending or failed.  \\ntags | An array of ModelVersionTag | Tags: Additional metadata key-value pairs for this `model_version`.  \\nrun_link | `STRING` | Run Link: Direct link to the run that generated this version. This field is set at model version creation time only for model versions whose source run is from a tracking server that is different from the registry server.  \\naliases | An array of `STRING` | Aliases pointing to this `model_version`.  \\nmodel_id | `STRING` | Optional model_id for model version that is used to link the registered model to the source logged model  \\nmodel_params | An array of ModelParam | Optional parameters for the model.  \\nmodel_metrics | An array of ModelMetric | Optional metrics for the model.  \\ndeployment_job_state | ModelVersionDeploymentJobState | Deployment job state for this model version.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 43428, \"end_char_idx\": 45466, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/rest-api.html","doc_id":"https://mlflow.org/docs/latest/rest-api.html","ref_doc_id":"https://mlflow.org/docs/latest/rest-api.html"}
{"_node_content":"{\"id_\": \"718c6ba5-e189-45ca-8fbf-4bc760658f9e\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/rest-api.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"9280e66609a7a65e141236963ad7b2533d22c543ba87cb1e924648644b79b5e4\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"d0ca33b4-f348-4cb2-ad52-1ff0a75dc606\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"99d28c2a9390e7966279e213f874865ce9bc84bf2aea3192d512198d60ec9587\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"d763e72a-0232-43de-af4f-e9b6881a82b6\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"36c8638752e9a3bcca3eea5bc0f4d26446a4a42508905257507017a89f909412\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"tags | An array of ModelVersionTag | Tags: Additional metadata key-value pairs for this `model_version`.  \\nrun_link | `STRING` | Run Link: Direct link to the run that generated this version. This field is set at model version creation time only for model versions whose source run is from a tracking server that is different from the registry server.  \\naliases | An array of `STRING` | Aliases pointing to this `model_version`.  \\nmodel_id | `STRING` | Optional model_id for model version that is used to link the registered model to the source logged model  \\nmodel_params | An array of ModelParam | Optional parameters for the model.  \\nmodel_metrics | An array of ModelMetric | Optional metrics for the model.  \\ndeployment_job_state | ModelVersionDeploymentJobState | Deployment job state for this model version.  \\n  \\n### ModelVersionDeploymentJobState\\n\\nField Name | Type | Description  \\n---|---|---  \\njob_id | `STRING` |   \\nrun_id | `STRING` |   \\njob_state | State |   \\nrun_state | DeploymentJobRunState |   \\ncurrent_task_name | `STRING` |   \\n  \\n### ModelVersionTag\\n\\nTag for a model version.\\n\\nField Name | Type | Description  \\n---|---|---  \\nkey | `STRING` | The tag key.  \\nvalue | `STRING` | The tag value.  \\n  \\n### Param\\n\\nParam associated with a run.\\n\\nField Name | Type | Description  \\n---|---|---  \\nkey | `STRING` | Key identifying this param.  \\nvalue | `STRING` | Value associated with this param.  \\n  \\n### RegisteredModel\\n\\nField Name | Type | Description  \\n---|---|---  \\nname | `STRING` | Unique name for the model.  \\ncreation_timestamp | `INT64` | Timestamp recorded when this `registered_model` was created.  \\nlast_updated_timestamp | `INT64` | Timestamp recorded when metadata for this `registered_model` was last updated.  \\nuser_id | `STRING` | User that created this `registered_model` NOTE: this field is not currently returned.  \\ndescription | `STRING` | Description of this `registered_model`.  \\nlatest_versions | An array of ModelVersion | Collection of latest model versions for each stage. Only contains models with current `READY` status.  \\ntags | An array of RegisteredModelTag | Tags: Additional metadata key-value pairs for this `registered_model`.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 44654, \"end_char_idx\": 46822, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/rest-api.html","doc_id":"https://mlflow.org/docs/latest/rest-api.html","ref_doc_id":"https://mlflow.org/docs/latest/rest-api.html"}
{"_node_content":"{\"id_\": \"d763e72a-0232-43de-af4f-e9b6881a82b6\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/rest-api.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"9280e66609a7a65e141236963ad7b2533d22c543ba87cb1e924648644b79b5e4\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"718c6ba5-e189-45ca-8fbf-4bc760658f9e\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"7bb2e29128b0dd6c3cbb984fba7aea1c77a4a7c758c2b5b7d2c90d966aaa2f98\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"306f498b-8ba7-43df-8884-78c428d409b6\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"a761e69959c7b28eb7bb921cd4c5d299389ff89fff14bfa29bb7b730563d2a36\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"value | `STRING` | Value associated with this param.  \\n  \\n### RegisteredModel\\n\\nField Name | Type | Description  \\n---|---|---  \\nname | `STRING` | Unique name for the model.  \\ncreation_timestamp | `INT64` | Timestamp recorded when this `registered_model` was created.  \\nlast_updated_timestamp | `INT64` | Timestamp recorded when metadata for this `registered_model` was last updated.  \\nuser_id | `STRING` | User that created this `registered_model` NOTE: this field is not currently returned.  \\ndescription | `STRING` | Description of this `registered_model`.  \\nlatest_versions | An array of ModelVersion | Collection of latest model versions for each stage. Only contains models with current `READY` status.  \\ntags | An array of RegisteredModelTag | Tags: Additional metadata key-value pairs for this `registered_model`.  \\naliases | An array of RegisteredModelAlias | Aliases pointing to model versions associated with this `registered_model`.  \\ndeployment_job_id | `STRING` | Deployment job id for this model.  \\ndeployment_job_state | State | Deployment job state for this model.  \\n  \\n### RegisteredModelAlias\\n\\nAlias for a registered model\\n\\nField Name | Type | Description  \\n---|---|---  \\nalias | `STRING` | The name of the alias.  \\nversion | `STRING` | The model version number that the alias points to.  \\n  \\n### RegisteredModelTag\\n\\nTag for a registered model\\n\\nField Name | Type | Description  \\n---|---|---  \\nkey | `STRING` | The tag key.  \\nvalue | `STRING` | The tag value.  \\n  \\n### Run\\n\\nA single run.\\n\\nField Name | Type | Description  \\n---|---|---  \\ninfo | RunInfo | Run metadata.  \\ndata | RunData | Run data.  \\ninputs | RunInputs | Run inputs.  \\noutputs | RunOutputs | Run outputs.  \\n  \\n### RunData\\n\\nRun data (metrics, params, and tags).\\n\\nField Name | Type | Description  \\n---|---|---  \\nmetrics | An array of Metric | Run metrics.  \\nparams | An array of Param | Run parameters.  \\ntags | An array of RunTag | Additional metadata key-value pairs.  \\n  \\n### RunInfo\\n\\nMetadata of a single run.\\n\\nField Name | Type | Description  \\n---|---|---  \\nrun_id | `STRING` | Unique identifier for the run.  \\nrun_uuid | `STRING` | [Deprecated, use run_id instead] Unique identifier for the run.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 46003, \"end_char_idx\": 48183, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/rest-api.html","doc_id":"https://mlflow.org/docs/latest/rest-api.html","ref_doc_id":"https://mlflow.org/docs/latest/rest-api.html"}
{"_node_content":"{\"id_\": \"306f498b-8ba7-43df-8884-78c428d409b6\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/rest-api.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"9280e66609a7a65e141236963ad7b2533d22c543ba87cb1e924648644b79b5e4\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"d763e72a-0232-43de-af4f-e9b6881a82b6\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"36c8638752e9a3bcca3eea5bc0f4d26446a4a42508905257507017a89f909412\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"5a9d391b-96cc-44d0-80bc-9e01f4edaa1d\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"9cbada0283fe047c7005524b23ffaed78bbce11754f47a3c7dd9a8f85cfa83df\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"value | `STRING` | The tag value.  \\n  \\n### Run\\n\\nA single run.\\n\\nField Name | Type | Description  \\n---|---|---  \\ninfo | RunInfo | Run metadata.  \\ndata | RunData | Run data.  \\ninputs | RunInputs | Run inputs.  \\noutputs | RunOutputs | Run outputs.  \\n  \\n### RunData\\n\\nRun data (metrics, params, and tags).\\n\\nField Name | Type | Description  \\n---|---|---  \\nmetrics | An array of Metric | Run metrics.  \\nparams | An array of Param | Run parameters.  \\ntags | An array of RunTag | Additional metadata key-value pairs.  \\n  \\n### RunInfo\\n\\nMetadata of a single run.\\n\\nField Name | Type | Description  \\n---|---|---  \\nrun_id | `STRING` | Unique identifier for the run.  \\nrun_uuid | `STRING` | [Deprecated, use run_id instead] Unique identifier for the run. This field will be removed in a future MLflow version.  \\nrun_name | `STRING` | The name of the run.  \\nexperiment_id | `STRING` | The experiment ID.  \\nuser_id | `STRING` | User who initiated the run. This field is deprecated as of MLflow 1.0, and will be removed in a future MLflow release. Use \\u00e2\\u0080\\u0098mlflow.user\\u00e2\\u0080\\u0099 tag instead.  \\nstatus | RunStatus | Current status of the run.  \\nstart_time | `INT64` | Unix timestamp of when the run started in milliseconds.  \\nend_time | `INT64` | Unix timestamp of when the run ended in milliseconds.  \\nartifact_uri | `STRING` | URI of the directory where artifacts should be uploaded. This can be a local path (starting with \\u00e2\\u0080\\u009c/\\u00e2\\u0080\\u009d), or a distributed file system (DFS) path, like `s3://bucket/directory` or `dbfs:/my/directory`. If not set, the local `./mlruns` directory is chosen.  \\nlifecycle_stage | `STRING` | Current life cycle stage of the experiment : OneOf(\\u00e2\\u0080\\u009cactive\\u00e2\\u0080\\u009d, \\u00e2\\u0080\\u009cdeleted\\u00e2\\u0080\\u009d)  \\n  \\n### RunInputs\\n\\nRun inputs.\\n\\nField Name | Type | Description  \\n---|---|---  \\ndataset_inputs | An array of DatasetInput | Dataset inputs to the Run.  \\nmodel_inputs | An array of ModelInput | Model inputs to the Run.  \\n  \\n### RunOutputs\\n\\nOutputs of a Run.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 47445, \"end_char_idx\": 49369, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/rest-api.html","doc_id":"https://mlflow.org/docs/latest/rest-api.html","ref_doc_id":"https://mlflow.org/docs/latest/rest-api.html"}
{"_node_content":"{\"id_\": \"5a9d391b-96cc-44d0-80bc-9e01f4edaa1d\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/rest-api.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"9280e66609a7a65e141236963ad7b2533d22c543ba87cb1e924648644b79b5e4\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"306f498b-8ba7-43df-8884-78c428d409b6\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"a761e69959c7b28eb7bb921cd4c5d299389ff89fff14bfa29bb7b730563d2a36\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"ec3af160-8179-4ebc-b809-b0ae4e4d9389\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"4ece800b5f4aecaa926779f637fbcba8429ba4a2278d8d0d9dc5e6020a54aaf6\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"end_time | `INT64` | Unix timestamp of when the run ended in milliseconds.  \\nartifact_uri | `STRING` | URI of the directory where artifacts should be uploaded. This can be a local path (starting with \\u00e2\\u0080\\u009c/\\u00e2\\u0080\\u009d), or a distributed file system (DFS) path, like `s3://bucket/directory` or `dbfs:/my/directory`. If not set, the local `./mlruns` directory is chosen.  \\nlifecycle_stage | `STRING` | Current life cycle stage of the experiment : OneOf(\\u00e2\\u0080\\u009cactive\\u00e2\\u0080\\u009d, \\u00e2\\u0080\\u009cdeleted\\u00e2\\u0080\\u009d)  \\n  \\n### RunInputs\\n\\nRun inputs.\\n\\nField Name | Type | Description  \\n---|---|---  \\ndataset_inputs | An array of DatasetInput | Dataset inputs to the Run.  \\nmodel_inputs | An array of ModelInput | Model inputs to the Run.  \\n  \\n### RunOutputs\\n\\nOutputs of a Run.\\n\\nField Name | Type | Description  \\n---|---|---  \\nmodel_outputs | An array of ModelOutput | Model outputs of the Run.  \\n  \\n### RunTag\\n\\nTag for a run.\\n\\nField Name | Type | Description  \\n---|---|---  \\nkey | `STRING` | The tag key.  \\nvalue | `STRING` | The tag value.  \\n  \\n### DeploymentJobRunState\\n\\nName | Description  \\n---|---  \\nDEPLOYMENT_JOB_RUN_STATE_UNSPECIFIED |   \\nNO_VALID_DEPLOYMENT_JOB_FOUND |   \\nRUNNING |   \\nSUCCEEDED |   \\nFAILED |   \\nPENDING |   \\nAPPROVAL |   \\n  \\n### ModelVersionStatus\\n\\nName | Description  \\n---|---  \\nPENDING_REGISTRATION | Request to register a new model version is pending as server performs background tasks.  \\nFAILED_REGISTRATION | Request to register a new model version has failed.  \\nREADY | Model version is ready for use.  \\n  \\n### RunStatus\\n\\nStatus of a run.\\n\\nName | Description  \\n---|---  \\nRUNNING | Run has been initiated.  \\nSCHEDULED | Run is scheduled to run at a later time.  \\nFINISHED | Run has completed.  \\nFAILED | Run execution failed.  \\nKILLED | Run killed by user.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 48642, \"end_char_idx\": 50381, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/rest-api.html","doc_id":"https://mlflow.org/docs/latest/rest-api.html","ref_doc_id":"https://mlflow.org/docs/latest/rest-api.html"}
{"_node_content":"{\"id_\": \"ec3af160-8179-4ebc-b809-b0ae4e4d9389\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/rest-api.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"9280e66609a7a65e141236963ad7b2533d22c543ba87cb1e924648644b79b5e4\", \"class_name\": \"RelatedNodeInfo\"}, \"2\": {\"node_id\": \"5a9d391b-96cc-44d0-80bc-9e01f4edaa1d\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"9cbada0283fe047c7005524b23ffaed78bbce11754f47a3c7dd9a8f85cfa83df\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"value | `STRING` | The tag value.  \\n  \\n### DeploymentJobRunState\\n\\nName | Description  \\n---|---  \\nDEPLOYMENT_JOB_RUN_STATE_UNSPECIFIED |   \\nNO_VALID_DEPLOYMENT_JOB_FOUND |   \\nRUNNING |   \\nSUCCEEDED |   \\nFAILED |   \\nPENDING |   \\nAPPROVAL |   \\n  \\n### ModelVersionStatus\\n\\nName | Description  \\n---|---  \\nPENDING_REGISTRATION | Request to register a new model version is pending as server performs background tasks.  \\nFAILED_REGISTRATION | Request to register a new model version has failed.  \\nREADY | Model version is ready for use.  \\n  \\n### RunStatus\\n\\nStatus of a run.\\n\\nName | Description  \\n---|---  \\nRUNNING | Run has been initiated.  \\nSCHEDULED | Run is scheduled to run at a later time.  \\nFINISHED | Run has completed.  \\nFAILED | Run execution failed.  \\nKILLED | Run killed by user.  \\n  \\n### State\\n\\nName | Description  \\n---|---  \\nDEPLOYMENT_JOB_CONNECTION_STATE_UNSPECIFIED |   \\nNOT_SET_UP | default state  \\nCONNECTED | connected job: job exists, owner has ACLs, and required job parameters are present  \\nNOT_FOUND | job was deleted OR owner had job ACLs removed  \\nREQUIRED_PARAMETERS_CHANGED | required job parameters were changed  \\n  \\n### ViewType\\n\\nView type for ListExperiments query.\\n\\nName | Description  \\n---|---  \\nACTIVE_ONLY | Default. Return only active experiments.  \\nDELETED_ONLY | Return only deleted experiments.  \\nALL | Get all experiments.  \\n  \\n[ Previous](java_api/index.html \\\"Java API\\\")\\n\\n* * *\\n\\n(C) MLflow Project, a Series of LF Projects, LLC. All rights reserved.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 49600, \"end_char_idx\": 51080, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/rest-api.html","doc_id":"https://mlflow.org/docs/latest/rest-api.html","ref_doc_id":"https://mlflow.org/docs/latest/rest-api.html"}
{"_node_content":"{\"id_\": \"e9d6306a-dcd6-4a03-aa65-9cb29732db95\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/system-metrics/index.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"66af96b8a5a5e2a9450a6953a325de5aa0857ddbc581614eeef933515244f1f2\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"\", \"mimetype\": \"text/plain\", \"start_char_idx\": 0, \"end_char_idx\": 0, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/system-metrics/index.html","doc_id":"https://mlflow.org/docs/latest/system-metrics/index.html","ref_doc_id":"https://mlflow.org/docs/latest/system-metrics/index.html"}
{"_node_content":"{\"id_\": \"825b5e1b-08c9-4d87-96ce-1c6a60f91248\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/tracking.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"66af96b8a5a5e2a9450a6953a325de5aa0857ddbc581614eeef933515244f1f2\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"\", \"mimetype\": \"text/plain\", \"start_char_idx\": 0, \"end_char_idx\": 0, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/tracking.html","doc_id":"https://mlflow.org/docs/latest/tracking.html","ref_doc_id":"https://mlflow.org/docs/latest/tracking.html"}
{"_node_content":"{\"id_\": \"da98aec8-d33f-47c2-af19-944ed2b727ce\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/tracking/artifacts-stores.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"66af96b8a5a5e2a9450a6953a325de5aa0857ddbc581614eeef933515244f1f2\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"\", \"mimetype\": \"text/plain\", \"start_char_idx\": 0, \"end_char_idx\": 0, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/tracking/artifacts-stores.html","doc_id":"https://mlflow.org/docs/latest/tracking/artifacts-stores.html","ref_doc_id":"https://mlflow.org/docs/latest/tracking/artifacts-stores.html"}
{"_node_content":"{\"id_\": \"39c79138-abdb-42fd-8ce1-acc436cc37ba\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/tracking/autolog.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"66af96b8a5a5e2a9450a6953a325de5aa0857ddbc581614eeef933515244f1f2\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"\", \"mimetype\": \"text/plain\", \"start_char_idx\": 0, \"end_char_idx\": 0, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/tracking/autolog.html","doc_id":"https://mlflow.org/docs/latest/tracking/autolog.html","ref_doc_id":"https://mlflow.org/docs/latest/tracking/autolog.html"}
{"_node_content":"{\"id_\": \"f30d7182-cb97-4420-b622-b5daafd51d65\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/tracking/backend-stores.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"66af96b8a5a5e2a9450a6953a325de5aa0857ddbc581614eeef933515244f1f2\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"\", \"mimetype\": \"text/plain\", \"start_char_idx\": 0, \"end_char_idx\": 0, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/tracking/backend-stores.html","doc_id":"https://mlflow.org/docs/latest/tracking/backend-stores.html","ref_doc_id":"https://mlflow.org/docs/latest/tracking/backend-stores.html"}
{"_node_content":"{\"id_\": \"9f7d80b6-6a01-4511-a053-2321a0c24675\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/tracking/data-api.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"66af96b8a5a5e2a9450a6953a325de5aa0857ddbc581614eeef933515244f1f2\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"\", \"mimetype\": \"text/plain\", \"start_char_idx\": 0, \"end_char_idx\": 0, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/tracking/data-api.html","doc_id":"https://mlflow.org/docs/latest/tracking/data-api.html","ref_doc_id":"https://mlflow.org/docs/latest/tracking/data-api.html"}
{"_node_content":"{\"id_\": \"d7145184-99e6-467f-8338-b4aaf60db995\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/tracking/server.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"66af96b8a5a5e2a9450a6953a325de5aa0857ddbc581614eeef933515244f1f2\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"\", \"mimetype\": \"text/plain\", \"start_char_idx\": 0, \"end_char_idx\": 0, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/tracking/server.html","doc_id":"https://mlflow.org/docs/latest/tracking/server.html","ref_doc_id":"https://mlflow.org/docs/latest/tracking/server.html"}
{"_node_content":"{\"id_\": \"35b2089e-b925-468d-a31c-305fae3d0e8b\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/tracking/tracking-api.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"66af96b8a5a5e2a9450a6953a325de5aa0857ddbc581614eeef933515244f1f2\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"\", \"mimetype\": \"text/plain\", \"start_char_idx\": 0, \"end_char_idx\": 0, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/tracking/tracking-api.html","doc_id":"https://mlflow.org/docs/latest/tracking/tracking-api.html","ref_doc_id":"https://mlflow.org/docs/latest/tracking/tracking-api.html"}
{"_node_content":"{\"id_\": \"0ad1e7ce-9c55-46bf-80cd-e6c9226127d9\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/tracking/tutorials/local-database.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"66af96b8a5a5e2a9450a6953a325de5aa0857ddbc581614eeef933515244f1f2\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"\", \"mimetype\": \"text/plain\", \"start_char_idx\": 0, \"end_char_idx\": 0, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/tracking/tutorials/local-database.html","doc_id":"https://mlflow.org/docs/latest/tracking/tutorials/local-database.html","ref_doc_id":"https://mlflow.org/docs/latest/tracking/tutorials/local-database.html"}
{"_node_content":"{\"id_\": \"2cce34c3-481f-491d-ace4-581a725a3165\", \"embedding\": null, \"metadata\": {}, \"excluded_embed_metadata_keys\": [], \"excluded_llm_metadata_keys\": [], \"relationships\": {\"1\": {\"node_id\": \"https://mlflow.org/docs/latest/tracking/tutorials/remote-server.html\", \"node_type\": \"4\", \"metadata\": {}, \"hash\": \"66af96b8a5a5e2a9450a6953a325de5aa0857ddbc581614eeef933515244f1f2\", \"class_name\": \"RelatedNodeInfo\"}}, \"metadata_template\": \"{key}: {value}\", \"metadata_separator\": \"\\n\", \"text\": \"\", \"mimetype\": \"text/plain\", \"start_char_idx\": 0, \"end_char_idx\": 0, \"metadata_seperator\": \"\\n\", \"text_template\": \"{metadata_str}\\n\\n{content}\", \"class_name\": \"TextNode\"}","_node_type":"TextNode","document_id":"https://mlflow.org/docs/latest/tracking/tutorials/remote-server.html","doc_id":"https://mlflow.org/docs/latest/tracking/tutorials/remote-server.html","ref_doc_id":"https://mlflow.org/docs/latest/tracking/tutorials/remote-server.html"}
