#!/usr/bin/env python3
"""
Fix for ZhipuAIEmbedding API key error in MLflow evaluation.

This script demonstrates the solution to the TypeError:
"ZhipuAIEmbedding.__init__() missing 1 required positional argument: 'api_key'"

The issue occurs because MLflow removes API keys during model serialization for security,
but ZhipuAIEmbedding requires the api_key parameter during deserialization.

Solution: Set API keys as environment variables before running evaluation.
"""

import os
import mlflow
from llama_index.llms.deepseek import DeepSeek
from llama_index.embeddings.zhipuai import ZhipuAIEmbedding
from llama_index.core import Settings

def setup_api_keys():
    """Set up API keys as environment variables."""
    # Set API keys as environment variables
    # MLflow expects these to be available during model loading
    os.environ["DEEPSEEK_API_KEY"] = "***********************************"
    os.environ["ZHIPUAI_API_KEY"] = "b416ef77699448858ba295b103ded93c.MHsFdHEvoG2MFKjT"
    print("✅ API keys set as environment variables")

def configure_models():
    """Configure LLM and embedding models."""
    # Configure models with API keys
    Settings.llm = DeepSeek("deepseek-chat", api_key="***********************************")
    Settings.embed_model = ZhipuAIEmbedding(
        "embedding-3", 
        dimensions=2048, 
        api_key="b416ef77699448858ba295b103ded93c.MHsFdHEvoG2MFKjT"
    )
    print("✅ Models configured successfully")

def test_model_serialization():
    """Test that models can be serialized and deserialized properly."""
    try:
        # Test embedding model
        test_text = "This is a test"
        embeddings = Settings.embed_model.get_text_embedding(test_text)
        print(f"✅ Embedding model working: {len(embeddings)} dimensions")
        
        # Test LLM
        response = Settings.llm.complete("Hello")
        print(f"✅ LLM working: {response.text[:50]}...")
        
        return True
    except Exception as e:
        print(f"❌ Model test failed: {e}")
        return False

def main():
    """Main function to demonstrate the fix."""
    print("🔧 Fixing ZhipuAIEmbedding API key error for MLflow evaluation")
    print("=" * 60)

    # Step 1: Set up API keys as environment variables
    setup_api_keys()

    # Step 2: Configure models
    configure_models()

    # Step 3: Test models
    if test_model_serialization():
        print("\n✅ All tests passed! The models should now work with MLflow evaluation.")
        print("\nNext steps:")
        print("1. Run the API key setup cell in your notebook")
        print("2. Make sure answer_correctness uses 'openai:/gpt-4o-mini' instead of 'deepseek:/deepseek-chat'")
        print("3. Re-run the MLflow evaluation code")
        print("4. Both ZhipuAIEmbedding and answer_correctness should now work properly")
    else:
        print("\n❌ Tests failed. Please check your API keys and model configuration.")

if __name__ == "__main__":
    main()
