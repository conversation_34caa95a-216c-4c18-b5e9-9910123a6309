class RetrieverChecker:
    VALID_RETRIEVERS = {"bm25", "dense", "sparse"}

    def __init__(self, retrievers):
        self.retrievers = retrievers

    def check_retrievers(self):
        if invalid_retrievers := set(self.retrievers) - self.VALID_RETRIEVERS:
            print(f"发现无效的 retrievers: {invalid_retrievers}")
        else:
            print("所有 retrievers 都合法！")


if __name__ == "__main__":
    # 测试一：包含无效的 retrievers
    checker1 = RetrieverChecker(["bm25", "my_custom", "dense"])
    checker1.check_retrievers()
    # 输出: 发现无效的 retrievers: {'my_custom'}

    # 测试二：全部合法
    checker2 = RetrieverChecker(["bm25", "dense"])
    checker2.check_retrievers()
    # 输出: 所有 retrievers 都合法！
